<?php

namespace BusinessDataModule\Responses;

use BusinessDataModule\Responses\Error\Error;
use Utils\Helpers\ArrayHelper;

class ErrorResponse
{
    /**
     * @var Error[]
     */
    private $errors;

    public function __construct(array $errors)
    {
        $this->errors = $errors;
    }

    public static function fromData(array $data): self
    {
        $errors = [];
        foreach (ArrayHelper::get($data, 'errors', []) as $error) {
            $errors[] = Error::fromData($error);
        }
        return new self($errors);
    }

    /**
     * @return Error[]
     */
    public function getErrors(): array
    {
        return $this->errors;
    }

    public function hasErrors(): bool
    {
        return !empty($this->getErrors());
    }

    public function getFirstError(): ?Error
    {
        $errors = $this->getErrors();
        if ($error = reset($errors)) {
            return $error;
        }

        return null;
    }
}