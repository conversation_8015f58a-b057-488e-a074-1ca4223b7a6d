<?php

namespace MailScanModule\Helpers;

class QueryHelper
{
    public const SEARCHING_REGEX_PATTERN_FOR_COMPANY_NAME = '[^a-zA-Z0-9\\s]+';

    public static function mapValuesToQuoted(array $values): array
    {
        return array_map(function (string $value) {
            return sprintf("'%s'", $value);
        }, $values);
    }

    public static function sanitizeCompanyNameField(?string $fieldContent): ?string
    {
        if (!is_string($fieldContent)) {
            return null;
        }

        return mb_strtoupper(
            trim(
                preg_replace(
                    '/\s+/',
                    ' ',
                    preg_replace(
                        sprintf('/%s/', QueryHelper::SEARCHING_REGEX_PATTERN_FOR_COMPANY_NAME),
                        '',
                        $fieldContent
                    )
                )
            )
        );
    }
}
