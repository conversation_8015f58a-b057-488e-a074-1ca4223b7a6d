<?php

namespace CompaniesHouseModule\Factories;

use CompaniesHouseModule\Dto\PersonData;
use CompaniesHouseModule\Entities\PersonalDetails;

class PersonalDetailsFactory
{
    public function createFromPersonData(PersonData $data): PersonalDetails
    {
        return new PersonalDetails(
            $data->getForename(),
            $data->getSurname(),
            $data->getDob(),
            $data->getNationality(),
            $data->getCountryOfResidence(),
            $data->getOccupation(),
            $data->getMiddleName(),
            $data->getTitle()
        );
    }
}
