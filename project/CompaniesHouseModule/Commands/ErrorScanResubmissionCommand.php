<?php

namespace CompaniesHouseModule\Commands;

use CompaniesHouseModule\Repositories\Legacy\EnvelopeRepository;
use CompaniesHouseModule\Updaters\Legacy\SubmissionUpdater;
use Entities\Company;
use Exception;
use FeatureModule\Feature;
use Psr\Log\LoggerInterface;
use Services\CompanyService;
use Services\SubmissionService;
use Utils\Helpers\ArrayHelper;
use Utils\Helpers\StringHelper;

/**
 * @description Resubmits error submissions for "schema scan line 8 column 68"
 */
class ErrorScanResubmissionCommand
{
    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var CompanyService
     */
    private $companyService;

    /**
     * @var SubmissionService
     */
    private $submissionService;

    /**
     * @var SubmissionUpdater
     */
    private $submissionUpdater;

    /**
     * @var EnvelopeRepository
     */
    private $envelopeRepository;

    public function __construct(
        LoggerInterface $logger,
        CompanyService $companyService,
        SubmissionService $submissionService,
        SubmissionUpdater $submissionUpdater,
        EnvelopeRepository $envelopeRepository
    ) {
        $this->logger = $logger;
        $this->companyService = $companyService;
        $this->submissionService = $submissionService;
        $this->submissionUpdater = $submissionUpdater;
        $this->envelopeRepository = $envelopeRepository;
    }

    public function resubmit()
    {
        if (Feature::isDisabled('companies_house_service')) {
            $this->logger->info('Command disabled (due to Companies House service is disabled).');
            return;
        }

        $items = 0;
        $errors = 0;

        foreach ($this->envelopeRepository->getChScanErrorFormSubmissions() as $row) {

            try {
                $items++;

                $item = $row->toArray();

                $formSubmissionId = ArrayHelper::get($item, 'form_submission_id');
                $companyId = ArrayHelper::get($item, 'company_id');

                $this->removeError($formSubmissionId, $companyId);

                $company = $this->companyService->getCompanyById($companyId);
                $this->sendFormSubmission($company, $formSubmissionId);

                $errors++;

            } catch (Exception $e) {

                if (StringHelper::contains($e->getMessage(), 'schema scan line 8 column 68')) {
                    $this->logger->debug($e->getMessage());
                    
                } else {
                    $this->logger->error($e->getMessage(), ['exception' => $e]);
                }

            }

        }

        $this->logger->debug('Form submissions finished.', ['items' => $items, 'errors' => $errors]);

    }

    private function removeError(int $formSubmissionId, int $companyId): void
    {
        $this->submissionUpdater->removeError($formSubmissionId);

        $this->logger->debug(
            'Form submission error removed',
            ['formSubmissionId' => $formSubmissionId, 'companyId' => $companyId]
        );
    }

    private function sendFormSubmission(Company $company, int $formSubmissionId): void
    {
        $submission = $this->submissionService->getFormSubmissionById($formSubmissionId);
        $oldSubmission = $this->submissionService->getOldFormSubmission(
            $submission->getCompany(),
            $submission->getId()
        );

        $oldSubmission->sendRequest();
        
        $this->logger->debug(
            'Form submission sent',
            ['formSubmissionId' => $formSubmissionId, 'companyId' => $company->getId()]
        );
        sleep(1);
    }

}