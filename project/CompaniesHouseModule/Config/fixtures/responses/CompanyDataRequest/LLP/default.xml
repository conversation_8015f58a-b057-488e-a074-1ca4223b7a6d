<?xml version="1.0" encoding="UTF-8" ?>
<GovTalkMessage xsi:schemaLocation="http://www.govtalk.gov.uk/CM/envelope                 http://xmlgw.companieshouse.gov.uk/v1-0/schema/Egov_ch-v2-0.xsd" xmlns="http://www.govtalk.gov.uk/CM/envelope" xmlns:dsig="http://www.w3.org/2000/09/xmldsig#" xmlns:gt="http://www.govtalk.gov.uk/schemas/govtalk/core" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" >
    <EnvelopeVersion>1.0</EnvelopeVersion>
    <Header>
        <MessageDetails>
            <Class>CompanyDataRequest</Class>
            <Qualifier>response</Qualifier>
            <TransactionID>16268478</TransactionID>
            <GatewayTimestamp>2018-03-13T13:55:57-00:00</GatewayTimestamp>
        </MessageDetails>
        <SenderDetails>
            <IDAuthentication>
                <SenderID>22688951f381cbb837f894a2a6a399d7</SenderID>
                <Authentication>
                    <Method>CHMD5</Method>
                    <Value></Value>
                </Authentication>
            </IDAuthentication>
            <EmailAddress><EMAIL></EmailAddress>
        </SenderDetails>
    </Header>
    <GovTalkDetails>
        <Keys/>
    </GovTalkDetails>
    <Body>
        <CompanyData>
            <CompanyNumber>{$company->getCompanyNumber()}</CompanyNumber>
            <CompanyName>{$company->getName()}</CompanyName>
            <CompanyCategory>LLP</CompanyCategory>
            <Jurisdiction>EW</Jurisdiction>
            <TradingOnMarket>false</TradingOnMarket>
            <DTR5Applies>false</DTR5Applies>
            <PSCExemptAsTradingOnRegulatedMarket>false</PSCExemptAsTradingOnRegulatedMarket>
            <PSCExemptAsSharesAdmittedOnMarket>false</PSCExemptAsSharesAdmittedOnMarket>
            <PSCExemptAsTradingOnUKRegulatedMarket>false</PSCExemptAsTradingOnUKRegulatedMarket>
            <MadeUpDate>2018-03-13</MadeUpDate>
            <NextDueDate>2018-03-27</NextDueDate>
            <RegisteredOfficeAddress>
                {include '../Blocks/address.xml', address => $registeredOfficeAddress}
            </RegisteredOfficeAddress>
            <SICCodes>
                <SICCode>86101</SICCode>
            </SICCodes>
            <Officers>
                {foreach $personDirectors as $member}
                <Member>
                    <Person>
                        <Title>{$member->getPersonalDetails()->getTitle()}</Title>
                        <Forename>{$member->getPersonalDetails()->getForename()}</Forename>
                        <Forename>{$member->getPersonalDetails()->getMiddleName()}</Forename>
                        <Surname>{$member->getPersonalDetails()->getSurname()}</Surname>
                        <ServiceAddress>
                            <SameAsRegisteredOffice>{$member->getAddress()->isEqual($member->getResidentialAddress())|boolToString}</SameAsRegisteredOffice>
                            <Address>
                                {include '../Blocks/address.xml', address => $member->getAddress()}
                            </Address>
                        </ServiceAddress>
                        <DOB>{$member->getPersonalDetails()->getDob()|datetime:'Y-m-d'}</DOB>
                        <Occupation>Company Director</Occupation>
                        <Nationality>{$member->getPersonalDetails()->getNationality()}</Nationality>
                        <CountryOfResidence>{$member->getPersonalDetails()->getCountryOfResidence()}</CountryOfResidence>
                        <ResidentialAddress>
                            <Address>
                                {include '../Blocks/address.xml', address => $member->getResidentialAddress()}
                            </Address>
                        </ResidentialAddress>
                    </Person>
                    <DesignatedInd>1</DesignatedInd>
                    <AppointmentDate>2015-04-01</AppointmentDate>
                </Member>
                {/foreach}
                {foreach $corporateDirectors as $member}
                <Member>
                    <Corporate>
                        <CorporateName>{$member->getCorporateDetails()->getCorporateName()}</CorporateName>
                        <Address>
                            {include '../Blocks/address.xml', address => $member->getAddress()}
                        </Address>
                        <CompanyIdentification>
                            <UK>
                                <PlaceRegistered>UNITED KINGDOM</PlaceRegistered>
                                <RegistrationNumber>06225876</RegistrationNumber>
                            </UK>
                        </CompanyIdentification>
                    </Corporate>
                    <DesignatedInd>1</DesignatedInd>
                    <AppointmentDate>2007-08-02</AppointmentDate>
                </Member>

                {/foreach}
                {foreach $personSecretaries as $member}
                <Secretary>
                    <Person>
                        <Title>{$member->getPersonalDetails()->getTitle()}</Title>
                        <Forename>{$member->getPersonalDetails()->getForename()}</Forename>
                        <Forename>{$member->getPersonalDetails()->getMiddleName()}</Forename>
                        <Surname>{$member->getPersonalDetails()->getSurname()}</Surname>
                        <ServiceAddress>
                            <SameAsRegisteredOffice>{$member->getAddress()->isEqual($member->getResidentialAddress())|boolToString}</SameAsRegisteredOffice>
                            <Address>
                                {include '../Blocks/address.xml', address => $member->getAddress()}
                            </Address>
                        </ServiceAddress>
                        <ResidentialAddress>
                            <Address>
                                {include '../Blocks/address.xml', address => $member->getResidentialAddress()}
                            </Address>
                        </ResidentialAddress>
                    </Person>
                    <DesignatedInd>1</DesignatedInd>
                    <AppointmentDate>2017-03-11</AppointmentDate>
                </Secretary>
                {/foreach}
                {foreach $corporateSecretaries as $member}
                <Secretary>
                    <Corporate>
                        <CorporateName>{$member->getCorporateDetails()->getCorporateName()}</CorporateName>
                        <Address>
                            {include '../Blocks/address.xml', address => $member->getAddress()}
                        </Address>
                        <CompanyIdentification>
                            <UK>
                                <PlaceRegistered>ENGLAND</PlaceRegistered>
                                <RegistrationNumber>07378448</RegistrationNumber>
                            </UK>
                        </CompanyIdentification>
                    </Corporate>
                    <DesignatedInd>1</DesignatedInd>
                    <AppointmentDate>2016-01-01</AppointmentDate>
                </Secretary>
                {/foreach}
            </Officers>
            <PSCs>
                {foreach $personPscs as $member}
                <PSC>
                    <PSCNotification>
                        <Individual>
                            <Title>{$member->getPersonalDetails()->getTitle()}</Title>
                            <Forename>{$member->getPersonalDetails()->getForename()}</Forename>
                            <Forename>{$member->getPersonalDetails()->getMiddleName()}</Forename>
                            <Surname>{$member->getPersonalDetails()->getSurname()}</Surname>
                            <ServiceAddress>
                                <SameAsRegisteredOffice>{$member->getAddress()->isEqual($member->getResidentialAddress())|boolToString}</SameAsRegisteredOffice>
                                <Address>
                                    {include '../Blocks/address.xml', address => $member->getAddress()}
                                </Address>
                            </ServiceAddress>
                            <DOB>{$member->getPersonalDetails()->getDob()|datetime:'Y-m-d'}</DOB>
                            <Nationality>{$member->getPersonalDetails()->getNationality()}</Nationality>
                            <CountryOfResidence>{$member->getPersonalDetails()->getCountryOfResidence()}</CountryOfResidence>
                            <ResidentialAddress>
                                <SameAsRegisteredOffice>{$member->getAddress()->isEqual($member->getResidentialAddress())|boolToString}</SameAsRegisteredOffice>
                                <Address>
                                    {include '../Blocks/address.xml', address => $member->getResidentialAddress()}
                                </Address>
                                <SecureAddressInd>false</SecureAddressInd>
                            </ResidentialAddress>
                        </Individual>
                        <NatureOfControls>
                            <NatureOfControl>OWNERSHIPOFSHARES_75TO100PERCENT</NatureOfControl>
                            <NatureOfControl>VOTINGRIGHTS_75TO100PERCENT</NatureOfControl>
                        </NatureOfControls>
                        <NotificationDate>2017-03-27</NotificationDate>
                    </PSCNotification>
                </PSC>
                {/foreach}
                {foreach $corporatePscs as $member}
                <PSC>
                    <PSCNotification>
                        <Corporate>
                            <CorporateName>{$member->getCorporateDetails()->getCorporateName()}</CorporateName>
                            <Address>
                                {include '../Blocks/address.xml', address => $member->getAddress()}
                            </Address>
                            <PSCCompanyIdentification>
                                <PSCPlaceRegistered>ENGLAND AND WALES</PSCPlaceRegistered>
                                <PSCRegistrationNumber>09090984</PSCRegistrationNumber>
                                <LawGoverned>ENGLAND &amp; WALES</LawGoverned>
                                <LegalForm>LIMITED BY SHARES</LegalForm>
                                <CountryOrState>ENGLAND AND WALES</CountryOrState>
                            </PSCCompanyIdentification>
                        </Corporate>
                        <NatureOfControls>
                            <NatureOfControl>OWNERSHIPOFSHARES_75TO100PERCENT</NatureOfControl>
                        </NatureOfControls>
                        <NotificationDate>2016-12-06</NotificationDate>
                    </PSCNotification>
                </PSC>
                {/foreach}
            </PSCs>
        </CompanyData>
    </Body>
</GovTalkMessage>