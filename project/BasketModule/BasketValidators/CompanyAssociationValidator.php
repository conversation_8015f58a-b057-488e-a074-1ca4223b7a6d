<?php

namespace BasketModule\BasketValidators;

use Libs\Basket;

class CompanyAssociationValidator implements IValidator
{
    /**
     * @param Basket $basket
     * @return ValidationResult
     */
    public function validate(Basket $basket)
    {
        $errors = [];

        if (!$this->isCompanyAssociationComplete($basket)) {
            //todo: take this from variable provider?
            $errors[] = "Your basket includes products that require you to specify the company you're buying for. Please complete the form below.";
        }

        return new ValidationResult($errors);
    }

    /**
     * @param Basket $basket
     * @return bool
     */
    public function isCompanyAssociationComplete(Basket $basket)
    {
        if (!$basket->hasPackageIncluded()) {

            foreach ($basket->getItems() as $item) {
                if (($item->requiredCompanyNumber || $item->requiredIncorporatedCompanyNumber)
                    && (!$item->getCompanyId() && !$item->getCompanyNumber())
                ) {
                    return FALSE;
                }
            }
        }
        return TRUE;
    }
}