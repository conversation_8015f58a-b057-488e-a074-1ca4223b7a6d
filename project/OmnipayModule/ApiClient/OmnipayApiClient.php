<?php

namespace OmnipayModule\ApiClient;

use Bootstrap\ApplicationLoader;
use CustomerModule\Entities\Settings\PaymentGatewaySetting;
use Entities\Payment\Token;
use HttpClient\ClientInterface;
use HttpClient\Exceptions\RequestException;
use HttpClient\Requests\RequestInterface;
use HttpClient\Requests\RequestOptions;
use OmnipayModule\Factories\OpayoPaymentResponseFactory;
use OmnipayModule\Providers\OmnipayCardProvider;
use OmnipayModule\Requests\OmnipayRequest;
use OmnipayModule\Responses\Response;
use PaymentModule\Contracts\IPaymentResponse;
use PaymentModule\Loggers\PaymentGatewayLogger;
use PaymentModule\PaymentTypes\OmnipayPayment;
use RouterModule\Helpers\IControllerHelper;
use SagePayToken\Token\Exception\FailedResponse;
use Utils\Helpers\ArrayHelper;

class OmnipayApiClient implements IOmnipayApiClient
{
    public const CUSTOMER = 'stripe/v1/customer'; // CORRECT
    public const PAYMENT = 'stripe/v1/payment'; // POSSIBLY NOT CORRECT
    public const PAYMENT_INTENT = 'stripe/v1/payment/intent'; // CORRECT
    public const STRIPE_PAYMENT_METHODS = 'stripe/v1/customer/payment_methods'; // CORRECT
    public const PAYMENT_METHODS = 'omnipay/v1/customer/payment_methods'; // CORRECT
    public const PAYMENT_METHODS_V2 = 'omnipay/v2/customer/payment_methods'; // CORRECT
    public const PAYMENT_CONFIRM = 'stripe/v2/payment/confirm'; // WAS INCORRECT - WAS OMNIPAY - CHANGED TO STRIPE - NOW CORRECT
    public const PAYMENT_CONFIRM_V1 = 'stripe/v1/payment/confirm'; // CORRECT
    public const PAYMENT_ORDER = 'stripe/v2/order'; // POSSIBLY NOT CORRECT?
    public const PAYMENT_ORDER_ASSOCIATE = 'stripe/v2/order/associate'; // CORRECT
    public const PAYMENT_REFUND = 'stripe/v1/payment/refund'; // CORRECT
    public const PAYMENT_AUTHORIZE = 'omnipay/v1/payment/authorize'; // DOES NOT EXIST ANYMORE
    public const STRIPE_DASHBOARD_REDIRECT = 'stripe/v1/redirect/customer?cms_customer_id='; // CORRECT
    public const CONNECT_PAYMENT_METHOD = 'stripe/v1/customer/connect_payment_method'; // CORRECT
    public const OPAYO_RETRIEVE_PAYMENT_ORDER = 'opayo/v1/order/retrieve'; // CORRECT
    public const OPAYO_PAY_WITH_TOKEN = 'opayo/v1/payment/create_with_token'; // CORRECT
    public const OPAYO_PAY_WITH_TOKEN_VO = 'opayo/v1/payment/create_with_token_vo'; // CORRECT
    public const OPAYO_EXPIRING_TOKENS = 'opayo/v1/customer/tokens/expiring'; // CORRECT
    public const OPAYO_REMOVE_TOKEN = 'opayo/v1/customer/tokens/remove'; // CORRECT
    public const OPAYO_SAVE_TOKENS = 'opayo/v1/customer/tokens/save'; // CORRECT
    public const VENDOR = 'vendor'; // CORRECT
    public const SET_VENDOR = 'omnipay/v1/vendor/set'; // CORRECT

    /**
     * @var ClientInterface
     */
    private $httpClient;

    /**
     * @var IResponseResolver
     */
    private $responseResolver;

    /**
     * @var PaymentGatewayLogger
     */
    private $paymentGatewayLogger;

    /**
     * @var IControllerHelper
     */
    private $controllerHelper;

    /**
     * @var array
     */
    private $config;

    /**
     * @var bool
     */
    private $isProduction;

    /**
     * @var array
     */
    private $ignoredResponseErrors;

    public function __construct(
        ClientInterface $httpClient,
        IResponseResolver $responseResolver,
        PaymentGatewayLogger $paymentGatewayLogger,
        IControllerHelper $controllerHelper,
        array $config,
        string $environment,
        array $ignoredResponseErrors = [],
    ) {
        $this->httpClient = $httpClient;
        $this->responseResolver = $responseResolver;
        $this->paymentGatewayLogger = $paymentGatewayLogger;
        $this->controllerHelper = $controllerHelper;
        $this->config = $config;
        $this->isProduction = $environment === ApplicationLoader::PRODUCTION;
        $this->ignoredResponseErrors = $ignoredResponseErrors;
    }

    public function createCustomer(int $customerId): Response
    {
        $request = new OmnipayRequest(
            'cms_customer_id',
            (string) $customerId,
            self::CUSTOMER,
            RequestInterface::POST,
            ['Content-Type' => 'application/json'],
            json_encode(['cms_customer_id' => $customerId]),
            !$this->isProduction ? new RequestOptions(RequestOptions::DEFAULT_TIMEOUT, false) : null
        );

        try {
            $response = $this->httpClient->sendRequest($request);

            $this->paymentGatewayLogger->logEvent(
                PaymentGatewayLogger::EVENT_API_CREATE_STRIPE_CUSTOMER,
                (array) $response,
                [
                    'endpoint' => self::CUSTOMER,
                    'customerId' => $customerId,
                ],
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY,
                false,
                false
            );

            return $this->responseResolver->resolveResponse($response);
        } catch (RequestException $e) {
            $errorData = $this->getErrorData(
                $e,
                self::CUSTOMER,
                ['customerId' => $customerId]
            );

            $this->paymentGatewayLogger->logEvent(
                PaymentGatewayLogger::EVENT_API_CREATE_STRIPE_CUSTOMER,
                [],
                $errorData,
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY,
                true
            );

            return $this->responseResolver->resolveError($e);
        }
    }

    public function getCustomerByStripeCustomerId(string $stripeCustomerId): Response
    {
        $request = new OmnipayRequest(
            'stripe_customer_id',
            $stripeCustomerId,
            self::CUSTOMER,
            RequestInterface::GET,
            ['Content-Type' => 'application/json'],
            null,
            !$this->isProduction ? new RequestOptions(RequestOptions::DEFAULT_TIMEOUT, false) : null,
            ['stripe_customer_id' => $stripeCustomerId]
        );

        try {
            $response = $this->httpClient->sendRequest($request);

            $this->paymentGatewayLogger->logEvent(
                PaymentGatewayLogger::EVENT_API_CREATE_STRIPE_CUSTOMER,
                (array) $response,
                [
                    'endpoint' => self::CUSTOMER,
                    'stripe_customer_id' => $stripeCustomerId,
                ],
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY,
                false,
                false
            );

            return $this->responseResolver->resolveResponse($response);
        } catch (RequestException $e) {
            $errorData = $this->getErrorData(
                $e,
                self::CUSTOMER,
                ['stripe_customer_id' => $stripeCustomerId]
            );

            $this->paymentGatewayLogger->logEvent(
                PaymentGatewayLogger::EVENT_API_CREATE_STRIPE_CUSTOMER,
                [],
                $errorData,
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY,
                true
            );

            return $this->responseResolver->resolveError($e);
        }
    }

    // TODO Refactor every place where this method shows up, replacing with getOmnipayCards
    public function getCards(
        string $customerId,
        ?string $groupBy = null,
        ?string $accountId = null,
        bool $isError = true,
        bool $includeInMantleReport = true,
    ): Response {
        $request = new OmnipayRequest(
            'cms_customer_id',
            (string) $customerId,
            self::STRIPE_PAYMENT_METHODS,
            RequestInterface::GET,
            ['Content-Type' => 'application/json'],
            null,
            !$this->isProduction ? new RequestOptions(RequestOptions::DEFAULT_TIMEOUT, false) : RequestOptions::timeout60(),
            [
                'cms_customer_id' => $customerId,
                'group_by' => $groupBy,
                'stripe_account_id' => $accountId,
            ]
        );

        try {
            $response = $this->httpClient->sendRequest($request);

            return $this->responseResolver->resolveResponse($response);
        } catch (RequestException $e) {
            $errorData = $this->getErrorData(
                $e,
                self::STRIPE_PAYMENT_METHODS,
                ['customerId' => $customerId, 'groupBy' => $groupBy, 'accountId' => $accountId]
            );

            $this->paymentGatewayLogger->logEvent(
                sprintf('%s - %s', PaymentGatewayLogger::EVENT_API_ERROR, __FUNCTION__),
                [],
                $errorData,
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY,
                $isError,
                $includeInMantleReport
            );

            return $this->responseResolver->resolveError($e);
        }
    }

    public function getPaymentIntent(
        string $paymentIntent,
        bool $isError = true,
        bool $includeInMantleReport = true,
    ): Response {
        $request = new OmnipayRequest(
            'stripe_payment_intent_id',
            $paymentIntent,
            self::PAYMENT,
            RequestInterface::GET,
            ['Content-Type' => 'application/json'],
            null,
            !$this->isProduction ? new RequestOptions(RequestOptions::DEFAULT_TIMEOUT, false) : RequestOptions::timeout60(),
            [
                'stripe_payment_intent_id' => $paymentIntent,
            ]
        );

        try {
            $response = $this->httpClient->sendRequest($request);

            return $this->responseResolver->resolveResponse($response);
        } catch (RequestException $e) {
            $errorData = $this->getErrorData(
                $e,
                self::STRIPE_PAYMENT_METHODS,
                ['paymentIntent' => $paymentIntent]
            );

            $this->paymentGatewayLogger->logEvent(
                sprintf('%s - %s', PaymentGatewayLogger::EVENT_API_ERROR, __FUNCTION__),
                [],
                $errorData,
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY,
                $isError,
                $includeInMantleReport
            );

            return $this->responseResolver->resolveError($e);
        }
    }

    public function createPaymentIntent(
        string $customerId,
        int $amount,
        bool $isError = true,
        bool $includeInMantleReport = true,
    ): Response {
        $request = new OmnipayRequest(
            'cms_customer_id',
            (string) $customerId,
            self::PAYMENT_INTENT,
            RequestInterface::POST,
            ['Content-Type' => 'application/json'],
            json_encode([
                'cms_customer_id' => $customerId,
                'amount' => $amount,
            ]),
            !$this->isProduction ? new RequestOptions(RequestOptions::DEFAULT_TIMEOUT, false) : null
        );

        try {
            $response = $this->httpClient->sendRequest($request);

            return $this->responseResolver->resolveResponse($response);
        } catch (RequestException $e) {
            $errorData = $this->getErrorData(
                $e,
                self::PAYMENT_INTENT,
                ['cms_customer_id' => $customerId, 'amount' => $amount]
            );

            $this->paymentGatewayLogger->logEvent(
                sprintf('%s - %s', PaymentGatewayLogger::EVENT_API_ERROR, __FUNCTION__),
                [],
                $errorData,
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY,
                $isError,
                $includeInMantleReport
            );

            return $this->responseResolver->resolveError($e);
        }
    }

    public function deleteCard(int $customerId, string $paymentMethodId): Response
    {
        $request = new OmnipayRequest(
            'cms_customer_id',
            (string) $customerId,
            self::STRIPE_PAYMENT_METHODS,
            RequestInterface::DELETE,
            ['Content-Type' => 'application/json'],
            null,
            !$this->isProduction ? new RequestOptions(RequestOptions::DEFAULT_TIMEOUT, false) : null,
            [
                'cms_customer_id' => $customerId,
                'payment_method_id' => $paymentMethodId,
            ]
        );

        try {
            $response = $this->httpClient->sendRequest($request);

            return $this->responseResolver->resolveResponse($response);
        } catch (RequestException $e) {
            $errorData = $this->getErrorData(
                $e,
                self::STRIPE_PAYMENT_METHODS,
                ['customerId' => $customerId, 'paymentMethodId' => $paymentMethodId]
            );

            $this->paymentGatewayLogger->logEvent(
                sprintf('%s - %s', PaymentGatewayLogger::EVENT_API_ERROR, __FUNCTION__),
                [],
                $errorData,
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY,
                true
            );

            return $this->responseResolver->resolveError($e);
        }
    }

    public function makePayment(
        string $customerId,
        string $cardId,
        int $amount,
        string $serializedBasket,
        string $origin,
        bool $isError = true,
        bool $includeInMantleReport = true,
        ?string $originUrl = null,
    ): Response {
        $requestData = [
            'cms_customer_id' => $customerId,
            'payment_method_id' => $cardId,
            'amount' => $amount,
            'serialized_basket' => $serializedBasket,
            'origin' => $origin,
        ];

        if ($originUrl) {
            $requestData['origin_url'] = $originUrl;
        }

        $request = new OmnipayRequest(
            'cms_customer_id',
            (string) $customerId,
            self::PAYMENT_CONFIRM,
            RequestInterface::POST,
            ['Content-Type' => 'application/json'],
            json_encode($requestData),
            !$this->isProduction ? new RequestOptions(RequestOptions::DEFAULT_TIMEOUT, false) : RequestOptions::timeout60()
        );

        try {
            $response = $this->httpClient->sendRequest($request);

            return $this->responseResolver->resolveResponse($response);
        } catch (RequestException $e) {
            $errorData = $this->getErrorData(
                $e,
                self::PAYMENT_CONFIRM,
                [
                    'customerId' => $customerId,
                    'paymentMethodId' => $cardId,
                    'amount' => $amount,
                    'origin' => $origin,
                    'serialized_basket' => $serializedBasket,
                    'origin_url' => $originUrl,
                ]
            );

            if ($isError) {
                $isError = !in_array(
                    ArrayHelper::get($errorData, 'status', ''),
                    $this->ignoredResponseErrors
                );
            }

            $this->paymentGatewayLogger->logEvent(
                sprintf('%s - %s', PaymentGatewayLogger::EVENT_API_ERROR, __FUNCTION__),
                [],
                $errorData,
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY,
                $isError,
                $includeInMantleReport
            );

            return $this->responseResolver->resolveError($e);
        }
    }

    public function confirmPaymentV1(
        string $customerId,
        string $cardId,
        int $amount,
        string $origin,
        bool $isError = true,
        bool $includeInMantleReport = true,
    ): Response {
        $request = new OmnipayRequest(
            'cms_customer_id',
            (string) $customerId,
            self::PAYMENT_CONFIRM_V1,
            RequestInterface::POST,
            ['Content-Type' => 'application/json'],
            json_encode([
                'cms_customer_id' => $customerId,
                'payment_method_id' => $cardId,
                'amount' => $amount,
                'metadata' => [
                    'project_origin' => $origin,
                ],
            ]),
            !$this->isProduction ? new RequestOptions(RequestOptions::DEFAULT_TIMEOUT, false) : RequestOptions::timeout60(),
        );

        try {
            $response = $this->httpClient->sendRequest($request);

            return $this->responseResolver->resolveResponse($response);
        } catch (RequestException $e) {
            $errorData = $this->getErrorData(
                $e,
                self::PAYMENT_CONFIRM_V1,
                ['customerId' => $customerId, 'paymentMethodId' => $cardId, 'amount' => $amount, 'origin' => $origin]
            );

            if ($isError) {
                $isError = !in_array(
                    ArrayHelper::get($errorData, 'status', ''),
                    $this->ignoredResponseErrors
                );
            }

            $this->paymentGatewayLogger->logEvent(
                sprintf('%s - %s', PaymentGatewayLogger::EVENT_API_ERROR, __FUNCTION__),
                [],
                $errorData,
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY,
                $isError,
                $includeInMantleReport
            );

            return $this->responseResolver->resolveError($e);
        }
    }

    public function refundPayment(string $orderId, int $amount): Response
    {
        $request = new OmnipayRequest(
            'cms_order_id',
            $orderId,
            self::PAYMENT_REFUND,
            RequestInterface::POST,
            ['Content-Type' => 'application/json'],
            json_encode([
                'cms_order_id' => $orderId,
                'amount' => $amount,
            ]),
            !$this->isProduction ? new RequestOptions(RequestOptions::DEFAULT_TIMEOUT, false) : RequestOptions::timeout60(),
        );

        try {
            $response = $this->httpClient->sendRequest($request);

            return $this->responseResolver->resolveResponse($response);
        } catch (RequestException $e) {
            $errorData = $this->getErrorData(
                $e,
                self::PAYMENT_REFUND,
                ['orderId' => $orderId, 'amount' => $amount]
            );

            $this->paymentGatewayLogger->logEvent(
                sprintf('%s - %s', PaymentGatewayLogger::EVENT_API_ERROR, __FUNCTION__),
                [],
                $errorData,
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY,
                true
            );

            return $this->responseResolver->resolveError($e);
        }
    }

    public function getOrder(string $paymentIntentId): Response
    {
        $request = new OmnipayRequest(
            'stripe_payment_intent_id',
            $paymentIntentId,
            self::PAYMENT_ORDER,
            RequestInterface::GET,
            ['Content-Type' => 'application/json'],
            null,
            !$this->isProduction ? new RequestOptions(RequestOptions::DEFAULT_TIMEOUT, false) : RequestOptions::timeout60(),
            [
                'stripe_payment_intent_id' => $paymentIntentId,
            ]
        );

        try {
            $response = $this->httpClient->sendRequest($request);

            return $this->responseResolver->resolveResponse($response);
        } catch (RequestException $e) {
            $errorData = $this->getErrorData(
                $e,
                self::PAYMENT_ORDER,
                ['paymentIntentId' => $paymentIntentId]
            );

            $this->paymentGatewayLogger->logEvent(
                sprintf('%s - %s', PaymentGatewayLogger::EVENT_API_ERROR, __FUNCTION__),
                [],
                $errorData,
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY
            );

            return $this->responseResolver->resolveError($e);
        }
    }

    public function getOrderConfirmation(string $omnipayOrderId): Response
    {
        $request = new OmnipayRequest(
            'omnipay_order_id',
            $omnipayOrderId,
            self::PAYMENT_ORDER,
            RequestInterface::GET,
            ['Content-Type' => 'application/json'],
            null,
            !$this->isProduction ? new RequestOptions(RequestOptions::DEFAULT_TIMEOUT, false) : RequestOptions::timeout60(),
            [
                'omnipay_order_id' => $omnipayOrderId,
            ]
        );

        try {
            $response = $this->httpClient->sendRequest($request);

            $this->paymentGatewayLogger->logEvent(
                PaymentGatewayLogger::EVENT_ORDER_CONFIRMATION_RESPONSE,
                [],
                [
                    'endpoint' => self::PAYMENT_ORDER,
                    'action' => 'getOrder',
                    'omnipay_order_id' => $omnipayOrderId,
                    'body' => $response->getBody(),
                ],
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY
            );

            return $this->responseResolver->resolveResponse($response);
        } catch (RequestException $e) {
            $errorData = $this->getErrorData(
                $e,
                self::PAYMENT_ORDER,
                ['omnipayOrderId' => $omnipayOrderId]
            );

            $this->paymentGatewayLogger->logEvent(
                sprintf('%s - %s', PaymentGatewayLogger::EVENT_API_ERROR, __FUNCTION__),
                [],
                $errorData,
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY
            );

            return $this->responseResolver->resolveError($e);
        }
    }

    public function createOrder(
        string $customerId,
        int $amount,
        string $paymentIntent,
        string $serializedBasket,
        string $origin,
        bool $creditPayment,
        bool $isError = true,
        bool $includeInMantleReport = true,
    ): Response {
        $request = new OmnipayRequest(
            'cms_customer_id',
            (string) $customerId,
            self::PAYMENT_ORDER,
            RequestInterface::POST,
            ['Content-Type' => 'application/json'],
            json_encode([
                'cms_customer_id' => $customerId,
                'amount' => $amount,
                'stripe_payment_intent_id' => $paymentIntent,
                'serialized_basket' => $serializedBasket,
                'origin' => $origin,
                'credit_payment' => $creditPayment,
            ]),
            !$this->isProduction ? new RequestOptions(RequestOptions::DEFAULT_TIMEOUT, false) : RequestOptions::timeout60(),
        );

        try {
            $response = $this->httpClient->sendRequest($request);

            return $this->responseResolver->resolveResponse($response);
        } catch (RequestException $e) {
            $errorData = $this->getErrorData(
                $e,
                self::PAYMENT_ORDER,
                [
                    'customerId' => $customerId,
                    'amount' => $amount,
                    'paymentIntent' => $paymentIntent,
                    'origin' => $origin,
                    'creditPayment' => $creditPayment,
                    'serializedBasket' => $serializedBasket,
                ]
            );

            $this->paymentGatewayLogger->logEvent(
                sprintf('%s - %s', PaymentGatewayLogger::EVENT_API_ERROR, __FUNCTION__),
                [],
                $errorData,
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY,
                $isError,
                $includeInMantleReport
            );

            return $this->responseResolver->resolveError($e);
        }
    }

    public function saveOrder(string $paymentIntentId, int $orderId, int $transactionId): Response
    {
        $request = new OmnipayRequest(
            'stripe_payment_intent_id',
            $paymentIntentId,
            self::PAYMENT_ORDER_ASSOCIATE,
            RequestInterface::POST,
            ['Content-Type' => 'application/json'],
            json_encode([
                'stripe_payment_intent_id' => $paymentIntentId,
                'cms_order_id' => $orderId,
                'cms_transaction_id' => $transactionId,
            ]),
            !$this->isProduction ? new RequestOptions(RequestOptions::DEFAULT_TIMEOUT, false) : RequestOptions::timeout60(),
        );

        try {
            $response = $this->httpClient->sendRequest($request);

            return $this->responseResolver->resolveResponse($response);
        } catch (RequestException $e) {
            $errorData = $this->getErrorData(
                $e,
                self::PAYMENT_ORDER_ASSOCIATE,
                ['orderId' => $orderId, 'paymentIntentId' => $paymentIntentId]
            );

            $this->paymentGatewayLogger->logEvent(
                sprintf('%s - %s', PaymentGatewayLogger::EVENT_API_ERROR, __FUNCTION__),
                [],
                $errorData,
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY,
                true
            );

            return $this->responseResolver->resolveError($e);
        }
    }

    public function connectPaymentMethod(
        string $customerId,
        string $paymentMethodId,
        string $paymentMethodFingerprint,
        string $accountId,
    ): Response {
        $request = new OmnipayRequest(
            'cms_customer_id',
            (string) $customerId,
            self::CONNECT_PAYMENT_METHOD,
            RequestInterface::POST,
            ['Content-Type' => 'application/json'],
            json_encode([
                'cms_customer_id' => $customerId,
                'payment_method_id' => $paymentMethodId,
                'payment_method_fingerprint' => $paymentMethodFingerprint,
                'stripe_account_id' => $accountId,
            ]),
            !$this->isProduction ? new RequestOptions(RequestOptions::DEFAULT_TIMEOUT, false) : RequestOptions::timeout60(),
        );

        try {
            $response = $this->httpClient->sendRequest($request);

            return $this->responseResolver->resolveResponse($response);
        } catch (RequestException $e) {
            $errorData = $this->getErrorData(
                $e,
                self::CONNECT_PAYMENT_METHOD,
                [
                    'customerId' => $customerId,
                    'paymentMethodId' => $paymentMethodId,
                    'paymentMethodFingerprint' => $paymentMethodFingerprint,
                    'accountId' => $accountId,
                ]
            );

            $this->paymentGatewayLogger->logEvent(
                sprintf('%s - %s', PaymentGatewayLogger::EVENT_API_ERROR, __FUNCTION__),
                [],
                $errorData,
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY
            );

            return $this->responseResolver->resolveError($e);
        }
    }

    public function getOpayoOrder(string $vendorTxCode): Response
    {
        $request = new OmnipayRequest(
            'vendor_tx_code',
            $vendorTxCode,
            self::OPAYO_RETRIEVE_PAYMENT_ORDER,
            RequestInterface::GET,
            ['Content-Type' => 'application/json'],
            null,
            !$this->isProduction ? new RequestOptions(RequestOptions::DEFAULT_TIMEOUT, false) : RequestOptions::timeout60(),
            ['vendor_tx_code' => $vendorTxCode]
        );

        try {
            $response = $this->httpClient->sendRequest($request);

            return $this->responseResolver->resolveResponse($response);
        } catch (RequestException $e) {
            $errorData = $this->getErrorData(
                $e,
                self::OPAYO_RETRIEVE_PAYMENT_ORDER,
                ['vendorTxCode' => $vendorTxCode]
            );

            $this->paymentGatewayLogger->logEvent(
                sprintf('%s - %s', PaymentGatewayLogger::EVENT_API_ERROR, __FUNCTION__),
                [],
                $errorData,
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY
            );

            return $this->responseResolver->resolveError($e);
        }
    }

    public function opayoPayWithToken(
        int $customerId,
        float $amount,
        string $serializedBasket,
        string $origin,
        array $opayoPaymentData,
        Token $token,
    ): IPaymentResponse {
        $request = new OmnipayRequest(
            'cms_customer_id',
            (string) $customerId,
            self::OPAYO_PAY_WITH_TOKEN,
            RequestInterface::POST,
            ['Content-Type' => 'application/json'],
            json_encode([
                'cms_customer_id' => $customerId,
                'amount' => $amount,
                'serialized_basket' => $serializedBasket,
                'origin' => $origin,
                'payment_data' => $opayoPaymentData,
            ]),
            !$this->isProduction ? new RequestOptions(RequestOptions::DEFAULT_TIMEOUT, false) : RequestOptions::timeout60(),
        );

        try {
            $response = $this->httpClient->sendRequest($request);

            return OpayoPaymentResponseFactory::createFromOpayoPaymentResponse($response, $token);
        } catch (RequestException $e) {
            $errorData = $this->getErrorData(
                $e,
                self::OPAYO_PAY_WITH_TOKEN,
                [
                    'customerId' => $customerId,
                    'amountInCents' => $amount,
                    'origin' => $origin,
                    'opayoPaymentData' => $opayoPaymentData,
                    'serializedBasket' => $serializedBasket,
                ]
            );

            $this->paymentGatewayLogger->logEvent(
                sprintf('%s - %s', PaymentGatewayLogger::EVENT_API_ERROR, __FUNCTION__),
                [],
                $errorData,
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY
            );

            throw new FailedResponse($errorData['message'], $errorData['code']);
        }
    }

    public function opayoPayWithTokenVO(
        int $customerId,
        float $amount,
        string $origin,
        array $opayoPaymentData,
    ): Response {
        $request = new OmnipayRequest(
            'cms_customer_id',
            (string) $customerId,
            self::OPAYO_PAY_WITH_TOKEN_VO,
            RequestInterface::POST,
            ['Content-Type' => 'application/json'],
            json_encode([
                'cms_customer_id' => $customerId,
                'amount' => $amount,
                'origin' => $origin,
                'payment_data' => $opayoPaymentData,
            ]),
            !$this->isProduction ? new RequestOptions(RequestOptions::DEFAULT_TIMEOUT, false) : RequestOptions::timeout60(),
        );

        try {
            $response = $this->httpClient->sendRequest($request);

            return $this->responseResolver->resolveResponse($response);
        } catch (RequestException $e) {
            $errorData = $this->getErrorData(
                $e,
                self::OPAYO_PAY_WITH_TOKEN_VO,
                [
                    'customerId' => $customerId,
                    'amountInCents' => $amount,
                    'origin' => $origin,
                    'opayoPaymentData' => $opayoPaymentData,
                ]
            );

            $this->paymentGatewayLogger->logEvent(
                sprintf('%s - %s', PaymentGatewayLogger::EVENT_API_ERROR, __FUNCTION__),
                [],
                $errorData,
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY
            );

            return $this->responseResolver->resolveError($e);
        }
    }

    public function saveTokens(string $customerId, array $tokens, bool $movedFromCms = false): Response
    {
        $request = new OmnipayRequest(
            'cms_customer_id',
            (string) $customerId,
            self::OPAYO_SAVE_TOKENS,
            RequestInterface::POST,
            ['Content-Type' => 'application/json'],
            json_encode([
                'cms_customer_id' => $customerId,
                'tokens' => $tokens,
                'moved_from_cms' => $movedFromCms,
            ]),
            !$this->isProduction ? new RequestOptions(RequestOptions::DEFAULT_TIMEOUT, false) : RequestOptions::timeout60(),
        );

        try {
            $response = $this->httpClient->sendRequest($request);

            return $this->responseResolver->resolveResponse($response);
        } catch (RequestException $e) {
            $errorData = $this->getErrorData(
                $e,
                self::OPAYO_SAVE_TOKENS,
                [
                    'customerId' => $customerId,
                    'tokens' => $tokens,
                ]
            );

            $this->paymentGatewayLogger->logEvent(
                sprintf('%s - %s', PaymentGatewayLogger::EVENT_API_ERROR, __FUNCTION__),
                [],
                $errorData,
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY
            );

            return $this->responseResolver->resolveError($e);
        }
    }

    public function getVendor(string $customerId): string
    {
        $request = new OmnipayRequest(
            'cms_customer_id',
            (string) $customerId,
            self::VENDOR,
            RequestInterface::POST,
            ['Content-Type' => 'application/json'],
            json_encode([
                'cms_customer_id' => $customerId,
            ]),
            !$this->isProduction ? new RequestOptions(RequestOptions::DEFAULT_TIMEOUT, false) : RequestOptions::timeout60(),
        );

        try {
            $response = $this->httpClient->sendRequest($request);
            $parsedResponse = json_decode($response->getBody(), true);

            return ArrayHelper::get($parsedResponse, 'vendor', OmnipayPayment::OMNIPAY_VENDOR_STRIPE);
        } catch (RequestException $e) {
            return OmnipayPayment::OMNIPAY_VENDOR_STRIPE;
        }
    }

    public function setVendor(string $customerId, string $vendor): Response
    {
        $request = new OmnipayRequest(
            'cms_customer_id',
            (string) $customerId,
            self::SET_VENDOR,
            RequestInterface::POST,
            ['Content-Type' => 'application/json'],
            json_encode([
                'cms_customer_id' => $customerId,
                'vendor' => $vendor,
            ]),
            !$this->isProduction ? new RequestOptions(RequestOptions::DEFAULT_TIMEOUT, false) : null
        );

        try {
            $response = $this->httpClient->sendRequest($request);

            return $this->responseResolver->resolveResponse($response);
        } catch (RequestException $e) {
            $errorData = $this->getErrorData(
                $e,
                self::SET_VENDOR,
                [
                    'customerId' => $customerId,
                    'vendor' => $vendor,
                ]
            );

            $this->paymentGatewayLogger->logEvent(
                sprintf('%s - %s', PaymentGatewayLogger::EVENT_API_ERROR, __FUNCTION__),
                [],
                $errorData,
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY
            );

            return $this->responseResolver->resolveError($e);
        }
    }

    public function getVendorImageUrl(?string $customerId): string
    {
        $omnipayUrl = ArrayHelper::get($this->config, 'url');
        $stripeImageUrl = sprintf(
            '%simg/vendors/powered-by-%s.png',
            $omnipayUrl, OmnipayPayment::OMNIPAY_VENDOR_STRIPE
        );

        if (!$customerId) {
            return $stripeImageUrl;
        }

        return sprintf('%simg/vendors/powered-by-%s.png', $omnipayUrl, $this->getVendor($customerId));
    }

    public function getPaymentMethods(
        string $customerId,
        string $groupBy = OmnipayCardProvider::GROUP_BY_PRECEDENCE,
        bool $isError = false,
    ): Response {
        $request = new OmnipayRequest(
            'cms_customer_id',
            (string) $customerId,
            self::PAYMENT_METHODS_V2,
            RequestInterface::GET,
            ['Content-Type' => 'application/json'],
            null,
            !$this->isProduction ? new RequestOptions(RequestOptions::DEFAULT_TIMEOUT, false) : RequestOptions::timeout60(),
            [
                'cms_customer_id' => $customerId,
                'group_by' => $groupBy,
                'by_precedence' => $groupBy === OmnipayCardProvider::GROUP_BY_PRECEDENCE,
            ]
        );

        try {
            $response = $this->httpClient->sendRequest($request);

            return $this->responseResolver->resolveResponse($response);
        } catch (RequestException $e) {
            $errorData = $this->getErrorData(
                $e,
                self::PAYMENT_METHODS_V2,
                [
                    'customerId' => $customerId,
                    'groupBy' => $groupBy,
                ]
            );

            $this->paymentGatewayLogger->logEvent(
                sprintf('%s - %s', PaymentGatewayLogger::EVENT_API_ERROR, __FUNCTION__),
                [],
                $errorData,
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY,
                $isError
            );

            return $this->responseResolver->resolveError($e);
        }
    }

    public function getExpiringTokens(string $fromDate, string $toDate): Response
    {
        $request = new OmnipayRequest(
            'from_date',
            $fromDate,
            self::OPAYO_EXPIRING_TOKENS,
            RequestInterface::GET,
            ['Content-Type' => 'application/json'],
            null,
            !$this->isProduction
                ? new RequestOptions(RequestOptions::DEFAULT_TIMEOUT, false)
                : RequestOptions::timeout60(),
            [
                'from_date' => $fromDate,
                'to_date' => $toDate,
            ]
        );

        try {
            $response = $this->httpClient->sendRequest($request);

            return $this->responseResolver->resolveResponse($response);
        } catch (RequestException $e) {
            $errorData = $this->getErrorData(
                $e,
                self::OPAYO_EXPIRING_TOKENS,
                [
                    'fromDate' => $fromDate,
                    'toDate' => $toDate,
                ]
            );

            $this->paymentGatewayLogger->logEvent(
                sprintf('%s - %s', PaymentGatewayLogger::EVENT_API_ERROR, __FUNCTION__),
                [],
                $errorData,
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY
            );

            return $this->responseResolver->resolveError($e);
        }
    }

    public function removeToken(int $customerId, string $tokenIdentifier): Response
    {
        $request = new OmnipayRequest(
            'cms_customer_id',
            (string) $customerId,
            self::OPAYO_REMOVE_TOKEN,
            RequestInterface::POST,
            ['Content-Type' => 'application/json'],
            null,
            !$this->isProduction ? new RequestOptions(RequestOptions::DEFAULT_TIMEOUT, false) : null,
            [
                'cms_customer_id' => $customerId,
                'identifier' => $tokenIdentifier,
            ]
        );

        try {
            $response = $this->httpClient->sendRequest($request);

            return $this->responseResolver->resolveResponse($response);
        } catch (RequestException $e) {
            $errorData = $this->getErrorData(
                $e,
                self::OPAYO_REMOVE_TOKEN,
                [
                    'customerId' => $customerId,
                    'identifier' => $tokenIdentifier,
                ]
            );

            $this->paymentGatewayLogger->logEvent(
                sprintf('%s - %s', PaymentGatewayLogger::EVENT_API_ERROR, __FUNCTION__),
                [],
                $errorData,
                PaymentGatewaySetting::PAYMENT_GATEWAY_OMNIPAY
            );

            return $this->responseResolver->resolveError($e);
        }
    }

    private function getErrorData(
        RequestException $e,
        string $endpoint,
        array $extraData = [],
    ): array {
        try {
            if ($e->getResponse()) {
                $parsedBody = json_decode($e->getResponse()->getBody(), true);
                if ($parsedBody) {
                    $code = ArrayHelper::get($parsedBody, 'code', $e->getCode());
                    $message = ArrayHelper::get($parsedBody, 'message', $e->getMessage());
                    $status = ArrayHelper::get($parsedBody, 'status', null);
                } else {
                    $code = $e->getCode();
                    $message = $e->getMessage();
                    $status = null;
                }

                $errorData = [
                    'code' => $code,
                    'message' => $message,
                    'status' => $status,
                ];
            } else {
                $errorData = [
                    'message' => $e->getMessage(),
                    'code' => $e->getCode(),
                ];
            }
        } catch (\Exception $e) {
            $errorData = [
                'message' => 'Unknown error',
                'code' => 0,
            ];
        }

        $errorData['endpoint'] = $endpoint;

        return array_merge($errorData, $extraData);
    }
}
