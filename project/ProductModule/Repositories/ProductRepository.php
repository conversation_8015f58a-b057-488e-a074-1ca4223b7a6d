<?php

namespace ProductModule\Repositories;

use Models\Products\AnnualReturn;
use ContentModule\Entities\Node;
use ContentModule\Factories\NodeQueryBuilder;
use ContentModule\Repositories\NodeRepository;
use DateTime;
use Doctrine\Common\Cache\Cache;
use Libs\Exceptions\EntityNotFound;
use Exceptions\Technical\NodeException;
use Models\Products\BasketProduct;
use Models\Products\IProduct;
use Models\Products\Product;
use Repositories\Nodes\NodeRepository as FNodeRepository;

class ProductRepository
{
    /**
     * @var NodeRepository
     */
    private $nodeRepository;

    /**
     * @var FNodeRepository
     */
    private $fnodeRepository;

    /**
     * @var Cache
     */
    private $cache;

    public function __construct(
        NodeRepository $nodeRepository,
        FNodeRepository $fnodeRepository,
        Cache $cache
    )
    {
        $this->nodeRepository = $nodeRepository;
        $this->fnodeRepository = $fnodeRepository;
        $this->cache = $cache;
    }

    /**
     * @param int $productId
     * @return Product
     * @throws NodeException
     */
    public function getProductById($productId)
    {
        /** @var Product $product */
        $product = Product::getProductById($productId);
        if (!$product || !$product->isOk2show()) {
            throw NodeException::nodeDoesNotExist($productId);
        }
        return $product;
    }

    /**
     * @param string $productName
     * @return Product
     * @throws NodeException
     */
    public function requiredByName(string $productName)
    {
        /** @var Node $node */
        $node = $this->nodeRepository->requiredByName($productName);
        return $this->getProductById($node->getId());
    }

    /**
     * @return Product
     * @throws NodeException
     * @throws EntityNotFound
     */
    public function getProductByName(string $productName)
    {
        /** @var Product $product */
        $product = $this->fnodeRepository->getProductByName($productName);

        if (!$product || !$product->isOk2show()) {
            throw NodeException::nodeDoesNotExist($productName);
        }

        return $product;
    }

    /**
     * @return int[]
     */
    public function getIdRequiredPackageIds(): array
    {
        $builder = new NodeQueryBuilder($this->nodeRepository->createSimpleBuilder());
        return $builder->getPackages()
            ->activeOnly(new DateTime('now'))
            ->withPropertyValue('isIdCheckRequired', 1)
            ->getIds();
    }

    /**
     * @return int[]
     */
    public function getNodesWithPriceIds(): array
    {
        $builder = new NodeQueryBuilder($this->nodeRepository->createSimpleBuilder());
        return $builder->getNodes()
            ->withExistingProperty('price')
            ->getIds();
    }

    public function getAnnualReturnProducts(): array
    {
        return [
            AnnualReturn::ANNUAL_RETURN_EXPRESS_SERVICE_PRODUCT => $this->fnodeRepository->getNodeById(AnnualReturn::ANNUAL_RETURN_EXPRESS_SERVICE_PRODUCT),
            AnnualReturn::ANNUAL_RETURN_SERVICE_PRODUCT => $this->fnodeRepository->getNodeById(AnnualReturn::ANNUAL_RETURN_SERVICE_PRODUCT),
            AnnualReturn::ANNUAL_RETURN_FEE_PRODUCT => $this->fnodeRepository->getNodeById(AnnualReturn::ANNUAL_RETURN_FEE_PRODUCT)
        ];
    }

    public function getSummaryInlineOffers(): array
    {
        return [
            Product::PRODUCT_CH_EXTRA_FEE_FOR_COMPANY_INCORPORATION => $this->fnodeRepository->getProductByName(Product::PRODUCT_CH_EXTRA_FEE_FOR_COMPANY_INCORPORATION),
            Product::PRODUCT_REGISTRATION_REVIEW => $this->fnodeRepository->getProductByName(Product::PRODUCT_REGISTRATION_REVIEW)
        ];
    }

    public function getMailboxPayToReleaseProduct(): IProduct|Product|BasketProduct
    {
        return $this->fnodeRepository->getProductByName(Product::MAILBOX_PAY_TO_RELEASE_PRODUCT);
    }
}
