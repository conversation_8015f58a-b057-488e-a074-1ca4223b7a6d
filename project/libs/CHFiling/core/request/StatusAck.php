<?php

namespace Libs\CHFiling\Core\Request;
use Libs\CHFiling\Core\Envelope;

final class StatusAck implements Request
{
    /**
     * @var string
     */
    private static $class = 'StatusAck';

    private function __construct()
    {
    }

    /**
     * @return StatusAck
     */
    public static function getNewStatucAck()
    {
        return new StatusAck();
    }

    /**
     * @return string
     */
    public function getClass()
    {
        return self::$class;
    }

    /**
     * @return string
     */
    public function getXml()
    {
        return '<StatusAck
                    xmlns="http://xmlgw.companieshouse.gov.uk"
                    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                    xsi:schemaLocation="http://xmlgw.companieshouse.gov.uk
                    http://xmlgw.companieshouse.gov.uk/v1-0/schema/forms/GetStatusAck-v1-1.xsd"></StatusAck>';
    }

    /**
     * @return string
     */
    public function sendRequest()
    {
        return Envelope::getNewEnvelope()->sendRequest($this);
    }
}
