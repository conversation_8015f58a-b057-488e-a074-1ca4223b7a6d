<?php

namespace OfferModule\Entities;

use Entities\Company;
use Entities\Customer;
use OfferModule\Forms\OffersForm;
use Symfony\Component\Validator\Context\ExecutionContextInterface;
use Utils\NetteSmartObject;

class OffersFormData extends NetteSmartObject
{
    /**
     * @var array
     */
    private $offers = [];

    /**
     * @var array
     */
    private $loans = [];

    /**
     * @var string
     */
    private $firstName;

    /**
     * @var string
     */
    private $lastName;

    /**
     * @var string
     */
    private $email;

    /**
     * @var string
     */
    private $phone;

    /**
     * @var string
     */
    private $postcode;

    /**
     * @var Customer
     */
    private $customer;

    /**
     * @var Company | null
     */
    private $company;

    /**
     * @param Customer $customer
     * @param Company|null $company
     */
    public function __construct(Customer $customer, Company $company = null)
    {
        $this->customer = $customer;
        $this->lastName = $customer->getLastName();
        $this->firstName = $customer->getFirstName();
        $this->email = $customer->getEmail();
        $this->postcode = $customer->getPostcode();
        $this->company = $company;
        $this->phone = $customer->getPhone();
    }

    /**
     * @param ExecutionContextInterface $context
     */
    public function validateSelections(ExecutionContextInterface $context)
    {
        $offersCount = count($this->getOffers());
        $loansCount = count($this->getLoans());

        if ($loansCount > 1) {
            $context->buildViolation('Sorry, you can only select one loan offer')->addViolation();
        }

        if (($offersCount + $loansCount) < 1) {
            $context->buildViolation('Please select at least one offer, or skip this step using the link below.')->addViolation();
        }
    }

    /**
     * @return array
     */
    public function getOffers()
    {
        return $this->offers;
    }

    /**
     * @param array $offers
     */
    public function setOffers($offers)
    {
        $this->offers = $offers;
    }

    /**
     * @return array
     */
    public function getLoans()
    {
        return $this->loans;
    }

    /**
     * @param array $loans
     */
    public function setLoans($loans)
    {
        $this->loans = $loans;
    }

    /**
     * @param string $firstName
     */
    public function setFirstName($firstName)
    {
        $this->firstName = $firstName;
    }

    /**
     * @param string $lastName
     */
    public function setLastName($lastName)
    {
        $this->lastName = $lastName;
    }

    /**
     * @param string $email
     */
    public function setEmail($email)
    {
        $this->email = $email;
    }

    /**
     * @param string $phone
     */
    public function setPhone($phone)
    {
        $this->phone = $phone;
    }

    /**
     * @param string $postcode
     */
    public function setPostcode($postcode)
    {
        $this->postcode = $postcode;
    }

    /**
     * @param Company $company
     */
    public function setCompany(?Company $company)
    {
        $this->company = $company;
    }

    /**
     * @return string
     */
    public function getFirstName()
    {
        return $this->firstName;
    }

    /**
     * @return string
     */
    public function getLastName()
    {
        return $this->lastName;
    }

    /**
     * @return string
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * @return string
     */
    public function getPhone()
    {
        return $this->phone;
    }

    /**
     * @return string
     */
    public function getPostcode()
    {
        return $this->postcode;
    }

    /**
     * @return string
     */
    public function getCompanyName(): ?string
    {
        return $this->company ? $this->company->getCompanyName(): null;
    }

    /**
     * @return Customer
     */
    public function getCustomer()
    {
        return $this->customer;
    }

    public function getCompany(): ?Company
    {
        return $this->company;
    }

    /**
     * @return boolean
     */
    public function hasTaxAssist()
    {
        return in_array(OffersForm::OFFER_TAX_ASSIST, $this->offers);
    }

    /**
     * @return boolean
     */
    public function hasWorldpay()
    {
        return in_array(OffersForm::OFFER_WORLDPAY, $this->offers);
    }

    /**
     * @return boolean
     */
    public function hasIwoca()
    {
        return in_array(OffersForm::OFFER_IWOCA, $this->offers);
    }

    /**
     * @return boolean
     */
    public function hasMoneypenny()
    {
        return in_array(OffersForm::OFFER_MONEYPENNY, $this->offers);
    }

    /**
     * @return boolean
     */
    public function hasTakepayments()
    {
        return in_array(OffersForm::OFFER_TAKEPAYMENTS, $this->offers);
    }

    /**
     * @return boolean
     */
    public function hasPearl()
    {
        return in_array(OffersForm::OFFER_PEARL, $this->offers);
    }

    /**
     * @return boolean
     */
    public function hasInsurance()
    {
        return $this->hasInsurance;
    }

    /**
     * @param boolean $hasInsurance
     */
    public function setHasInsurance($hasInsurance)
    {
        $this->hasInsurance = $hasInsurance;
    }
}
