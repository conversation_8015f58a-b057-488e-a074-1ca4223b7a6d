<?php

namespace CompanyModule\Providers;

use DateInterval;
use Entities\Company;
use Utils\Date;

class ConfirmationStatementProvider
{
    public function getReturnsNextDueDate(Company $company): Date
    {
        return $company->getReturnsNextDueDate()
                ? Date::createFromDateTime($company->getReturnsNextDueDate())
                : new Date($company->getIncorporationDate()->add(DateInterval::createFromDateString('12 months'))->format('Y-m-d'));
    }

    public function getDaysToNextReturnDate(Company $company): int
    {
        return (int) (new Date())->diff($this->getReturnsNextDueDate($company))->format("%r%a");
    }
}