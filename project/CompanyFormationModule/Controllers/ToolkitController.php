<?php

namespace CompanyFormationModule\Controllers;

use BusinessServicesModule\Providers\ChoicesProvider;
use CompanyFormationModule\Providers\StepProvider;
use Entities\Company;
use Exceptions\Technical\NodeException;
use OfferModule\Mappers\ToolkitOfferMapper;
use Psr\Log\LoggerInterface;
use RouterModule\Helpers\ControllerHelper;
use TemplateModule\Renderers\IRenderer;
use ToolkitOfferModule\Forms\OffersForm;
use ToolkitOfferModule\Services\ToolkitOfferService;

class ToolkitController
{
    /**
     * @var ControllerHelper
     */
    private $controllerHelper;

    /**
     * @var ToolkitOfferService
     */
    private $toolkitOffersService;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var IRenderer
     */
    private $renderer;

    public function __construct(
        IRenderer $renderer,
        ControllerHelper $controllerHelper,
        ToolkitOfferService $toolkitOffersService,
        LoggerInterface $logger
    ) {
        $this->controllerHelper = $controllerHelper;
        $this->toolkitOffersService = $toolkitOffersService;
        $this->logger = $logger;
        $this->renderer = $renderer;
    }

    public function render(Company $company)
    {
        $allOffers = $this->toolkitOffersService->getAvailableCompanyOffers($company);
        $companyOffers = $company->getSelectedToolkitOffers();
        $offerTypesAvailable = ToolkitOfferMapper::formatChoices($allOffers);
        $offerTypesSelected = ToolkitOfferMapper::formatSelected(
            $company->toolkitOffersAlreadyChosen() ? $companyOffers : []
        );
        $choices = ToolkitOfferMapper::formatChoiceIds($allOffers);

        $form = $this->controllerHelper->buildForm(
            OffersForm::class,
            null,
            ['offers' => array_flip($offerTypesAvailable), 'selectedOffers' => array_flip($offerTypesSelected)]
        );

        if ($form->isSubmitted() && $form->isValid()) {
            try {
                $this->toolkitOffersService->assignOffersToCompany(
                    $company,
                    $form['offers']->getData()
                );
                return $this->controllerHelper->redirectionTo(
                    'company_formation_module.next_step',
                    ['company' => $company->getId(), 'step' => StepProvider::STEP_TOOLKIT]
                );

            } catch (NodeException $e) {
                $this->logger->error('Toolkit offers', ['e' => $e]);
                return $this->controllerHelper->redirectionTo(
                    'company_formation_module.next_step',
                    ['company' => $company->getId(), 'step' => StepProvider::STEP_TOOLKIT]
                );
            }
        }
        $variables = [];
        $variables['form'] = $form->createView();
        $variables['choices'] = $choices;
        $variables['selectedOffers'] = $offerTypesSelected;

        return $this->renderer->render($variables);
    }
}
