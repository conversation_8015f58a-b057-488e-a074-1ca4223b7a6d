<?php

namespace CompanyFormationModule\ValueObjects;

use Libs\CHFiling\Core\Request\Form\Incorporation\IncorporationPersonDirector;
use Libs\CHFiling\Core\UtilityClass\NatureOfControl;
use Libs\CHFiling\Core\UtilityClass\PersonMember;

class PossibleIncorporationPersonPsc
{
    /**
     * @var IncorporationPersonDirector
     */
    private $director;

    /**
     * @var NatureOfControl
     */
    private $natureOfControl;

    /**
     * @param IncorporationPersonDirector $director
     * @param NatureOfControl $natureOfControl
     */
    public function __construct(
        IncorporationPersonDirector $director,
        NatureOfControl $natureOfControl
    )
    {
        $this->natureOfControl = $natureOfControl;
        $this->director = $director;
    }

    /**
     * @return IncorporationPersonDirector
     */
    public function getDirector()
    {
        return $this->director;
    }

    /**
     * @return NatureOfControl
     */
    public function getNatureOfControl()
    {
        return $this->natureOfControl;
    }

    /**
     * @param PersonMember $person
     * @return bool
     */
    public function isSamePerson(PersonMember $person)
    {
        return $this->getDirector()->isSame<PERSON>erson($person);
    }
}
