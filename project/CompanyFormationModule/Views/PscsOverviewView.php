<?php

namespace CompanyFormationModule\Views;

use CompanyFormationModule\Entities\PscCorporate;
use CompanyFormationModule\Entities\PscPerson;
use Entities\Register\Psc\CorporatePsc;
use Entities\Register\Psc\PersonPsc;
use PaymentModule\Views\InlinePayment\InlinePayment;

class PscsOverviewView
{
    public function __construct(
        private array $personPscs,
        private array $corporatePscs,
        private bool $hasNoPscReason,
        private bool $isUpsellEligible,
        private InlinePayment $inlinePayment,
        private bool $hasIncompleteAppointments = false
    ) {}

    public function hasNoPscReason(): bool
    {
        return $this->hasNoPscReason;
    }

    public function hasPersonPscs(): bool
    {
        return count($this->personPscs) > 0;
    }

    public function hasCorporatePscs(): bool
    {
        return count($this->corporatePscs) > 0;
    }

    /**
     * @return PersonPsc[]
     */
    public function getPersonPscs(): array
    {
        return $this->personPscs;
    }

    /**
     * @return CorporatePsc[]
     */
    public function getCorporatePscs(): array
    {
        return $this->corporatePscs;
    }

    public function isUpsellEligible(): bool
    {
        return $this->isUpsellEligible;
    }

    public function getInlinePayment(): InlinePayment
    {
        return $this->inlinePayment;
    }

    public function getInlinePaymentStatus(): string
    {
        return $this->inlinePayment->getStatus();
    }

    public function hasIncompleteAppointments(): bool
    {
        return $this->hasIncompleteAppointments;
    }
}
