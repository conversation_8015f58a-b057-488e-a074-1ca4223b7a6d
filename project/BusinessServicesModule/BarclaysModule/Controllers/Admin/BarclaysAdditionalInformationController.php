<?php

declare(strict_types=1);

namespace BusinessServicesModule\BarclaysModule\Controllers\Admin;

use BusinessServicesModule\BarclaysModule\Services\BarclaysLeadService;
use CompaniesHouseModule\Entities\DirectorPerson;
use CompaniesHouseModule\Repositories\MemberRepository;
use CompanyIncorporationModule\Services\BusinessServices\AdvertService;
use CompanyIncorporationModule\Validators\AddressValidator;
use Entities\Company;
use FeatureModule\Feature;
use RouterModule\Domain\FlashMessage;
use RouterModule\Helpers\IControllerHelper;
use Symfony\Component\HttpFoundation\Response;
use TemplateModule\Renderers\IRenderer;

readonly class BarclaysAdditionalInformationController
{
    public function __construct(
        private IRenderer $renderer,
        private IControllerHelper $controllerHelper,
        private MemberRepository $memberRepository,
        private AdvertService $advertService,
        private BarclaysLeadService $barclaysLeadService,
    ) {}

    public function render(Company $company): Response
    {
        try {
            $this->validateCompany($company);

            return $this->renderer->render(
                [
                    'company' => $company,
                    'directors' => $this->getDirectors($company),
                    'advertId' => Feature::isEnabled('admin_barclays_leads_use_bdg')
                        ? $this->getBarclaysAdvertId($company)
                        : null,
                ]
            );
        } catch (\Throwable $e) {
            $this->controllerHelper->notify(FlashMessage::error($e->getMessage()));

            return $this->controllerHelper->redirectionTo('admin_barclays_leads');
        }
    }

    /**
     * @throws \Exception
     */
    private function getDirectors(Company $company): array
    {
        $directors = [];

        /** @var DirectorPerson $director */
        foreach ($this->memberRepository->getCompanyMembersByEntityName($company, DirectorPerson::class) as $director) {
            if (!AddressValidator::isUk($director->getResidentialAddress()->getCountry())) {
                continue;
            }

            $directors[$director->getId()] = $director->getFullName();
        }

        if (empty($directors)) {
            throw new \Exception(\sprintf('Company %s - %s has no Person Directors', $company->getId(), $company->getName()));
        }

        return $directors;
    }

    private function getBarclaysAdvertId($company): ?string
    {
        if (!$company->isBasicPackage()) {
            return null;
        }

        try {
            foreach ($this->advertService->getAdvertsForIncorporatedCompany($company)->getAdverts() as $advert) {
                if (!$advert->isBarclays()) {
                    return $advert->getId();
                }
            }

            return null;
        } catch (\Throwable $e) {
            return null;
        }
    }

    private function validateCompany(Company $company): void
    {
        if (!$company->isIncorporated()) {
            throw new \InvalidArgumentException('Company must be incorporated');
        }

        if ($this->barclaysLeadService->hasLead($company)) {
            throw new \InvalidArgumentException('Lead already processed');
        }
    }
}
