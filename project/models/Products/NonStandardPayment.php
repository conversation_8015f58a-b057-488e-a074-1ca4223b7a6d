<?php

namespace Models\Products;

use AdminModule\Controlers\NonStandardPaymentAdminControler;
use Models\Products\Product;

class NonStandardPayment extends Product
{

    public function isOk2show()
    {
        if (
            $this->adminControler != NonStandardPaymentAdminControler::class
            && $this->getAdminControllerNamespace() != NonStandardPaymentAdminControler::class
        ) {
            return FALSE;
        }
        return parent::isOk2show();
    }

}
