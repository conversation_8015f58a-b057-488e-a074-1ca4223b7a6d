<?php

declare(strict_types=1);

namespace CompanyIncorporationModule\Factories;

use Entities\Company;
use MoneyPennyNumbersModule\Entities\MoneyPennyCleverLeadDetails;

class MoneyPennyCleverLeadDetailsFactory
{
    public function make(
        Company $company,
        string $targetNumber,
        string $cleverNumber,
        string $cmsPaymentMethod,
    ): MoneyPennyCleverLeadDetails {
        return new MoneyPennyCleverLeadDetails($company, $targetNumber, $cleverNumber, $cmsPaymentMethod);
    }
}
