<div class="mb-3" id="mailForwardingOffer">
  <div
    id="mailForwardingCompleted"
    class="card rounded-4 p-4 {if ($currentStep == 'REGISTERED_OFFICE_FILLED' || $mailForwardingOfferStatus == 'TAKEN' || $mailForwardingOfferStatus == 'SKIPPED')} d-block {else} d-none {/if}"
  >
    <div class="card-body">
      <p class="fw-semibold fs-5 card-title">
        Mail Forwarding Address
        <button type="button" class="btn btn-link {if $currentStep == 'REGISTERED_OFFICE_FILLED'} hidden {/if}" id="editMailforwarding">Edit</button>
        <span
            id="mailForwardingCompletedText"
            class="float-end {if $mailForwardingOfferStatus == 'TAKEN'} nip-completed-card-text {/if} {if $mailForwardingOfferStatus == 'TAKEN'|| $mailForwardingOfferStatus == 'SKIPPED'} d-block {else} d-none {/if}"
        >
            {if $mailForwardingOfferStatus == 'TAKEN'}
              <span class="nip-completed-card-text">COMPLETED</span>
            {else}
              SKIPPED
            {/if}
          </span>
      </p>
      <p
        id="mailForwardingAddress"
        class="card-text {if $mailForwardingOfferStatus == 'TAKEN'} d-block {else} d-none {/if}"
      >
        <span class="nip-purple-text fw-semibold">20 Wenlock Road, London, N1 7GU</span>
      </p>
    </div>
  </div>

  <div
    id="mailForwardingComponent"
    class="{if $currentStep != 'REGISTERED_OFFICE_FILLED' && (($mailForwardingOfferStatus == 'NOT_TAKEN' && $mailForwardingOfferStatus != 'SKIPPED' && $mailForwardingOfferStatus != 'TAKEN'))} d-block {else} d-none{/if}"
  >
    <mail-forwarding-address-component
      company-id="{$company->getId()}"
      skip-cta-label="{$mailForwardingSkipCtaLabel}"
      continue-cta-label="{$mailForwardingContinueCtaLabel}"
      v-bind:is-llp="{$company->isLlpType()|booleanToString}"
      :serialized-current-step="{json_encode($serializedCurrentStep)}"
      :is-visible="{$currentStep != 'REGISTERED_OFFICE_FILLED' && (($mailForwardingOfferStatus == 'NOT_TAKEN' && $mailForwardingOfferStatus != 'SKIPPED' && $mailForwardingOfferStatus != 'TAKEN')) ? 'true' : 'false'}"
    >
    </mail-forwarding-address-component>
  </div>
</div>