<?php

namespace BankingModule\TideModule\Providers;

use BankingModule\Entities\CompanyCustomer;

class ApplicationDataProvider
{
    /**
     * @var ApplicantDataProvider
     */
    private $applicantDataProvider;

    /**
     * @var CompanyDataProvider
     */
    private $companyDataProvider;

    /**
     * @param ApplicantDataProvider $applicantDataProvider
     * @param CompanyDataProvider $companyDataProvider
     */
    public function __construct(ApplicantDataProvider $applicantDataProvider, CompanyDataProvider $companyDataProvider)
    {
        $this->applicantDataProvider = $applicantDataProvider;
        $this->companyDataProvider = $companyDataProvider;
    }

    /**
     * @param CompanyCustomer $companyCustomer
     * @return array
     */
    public function getData(CompanyCustomer $companyCustomer)
    {
        return [
            'applicant' => $this->applicantDataProvider->getData($companyCustomer),
            'company' => $this->companyDataProvider->getData($companyCustomer)
        ];
    }
}