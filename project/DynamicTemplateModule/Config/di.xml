<?xml version="1.0" ?>
<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <defaults public="true" />
        <service class="DynamicTemplateModule\Deciders\DynamicTemplateDecider" id="dynamic_template_module.deciders.dynamic_template_decider"/>

        <service class="DynamicTemplateModule\Factories\PageComponentsFactory" id="dynamic_template_module.factories.page_components_factory">
            <argument id="product_module.repositories.product_repository" type="service"/>
            <argument id="dynamic_template_module.deciders.dynamic_template_decider" type="service"/>
        </service>

        <service class="DynamicTemplateModule\Providers\DynamicTemplateProvider" id="dynamic_template_module.providers.dynamic_template_provider">
            <argument>%root%/storage/template_variables/DynamicTemplateModule/</argument>
        </service>
        <service class="DynamicTemplateModule\Facades\DynamicTemplateFacade" id="dynamic_template_module.facades.dynamic_template_facade">
            <argument id="dynamic_template_module.providers.dynamic_template_provider" type="service"/>
            <argument id="content_module.variable_provider" type="service"/>
            <argument id="dynamic_template_module.deciders.dynamic_template_decider" type="service"/>
            <argument id="dynamic_template_module.factories.page_components_factory" type="service"/>
        </service>
        <service class="DynamicTemplateModule\Controllers\DynamicTemplateController" id="dynamic_template_module.controllers.dynamic_template_controller">
            <argument id="templating_module.front_renderer" type="service"/>
            <argument id="router_module.helpers.controller_helper" type="service"/>
            <argument id="dynamic_template_module.facades.dynamic_template_facade" type="service"/>
            <argument>%environment%</argument>
        </service>
    </services>
</container>
