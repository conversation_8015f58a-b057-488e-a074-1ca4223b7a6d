<?php

namespace DynamicTemplateModule\Facades;

use ContentModule\VariableProviders\MarkdownParsedVariableProvider;
use DynamicTemplateModule\Deciders\DynamicTemplateDecider;
use DynamicTemplateModule\Dto\PageContent;
use DynamicTemplateModule\Factories\PageContentFactory;
use DynamicTemplateModule\Providers\DynamicTemplateProvider;
use InvalidArgumentException;
use Symfony\Component\Yaml\Exception\ParseException;
use DynamicTemplateModule\Factories\PageComponentsFactory;
use Exceptions\Technical\NodeException;

class DynamicTemplateFacade
{
    /**
     * @var DynamicTemplateProvider
     */
    private $dynamicTemplateProvider;

    /**
     * @var MarkdownParsedVariableProvider
     */
    private $markdownParsedVariableProvider;

    /**
     * @var DynamicTemplateDecider
     */
    private $dynamicTemplateDecider;

    /**
     * @var PageComponentsFactory
     */
    private $pageComponentsFactory;

    public function __construct(
        DynamicTemplateProvider $dynamicTemplateProvider,
        MarkdownParsedVariableProvider $markdownParsedVariableProvider,
        DynamicTemplateDecider $dynamicTemplateDecider,
        PageComponentsFactory $pageComponentsFactory
    )
    {
        $this->dynamicTemplateProvider = $dynamicTemplateProvider;
        $this->markdownParsedVariableProvider = $markdownParsedVariableProvider;
        $this->dynamicTemplateDecider = $dynamicTemplateDecider;
        $this->pageComponentsFactory = $pageComponentsFactory;
    }

    public function getContent(string $content): ?PageContent
    {
        try {
            $page = $this->dynamicTemplateProvider->getPageContent($content);
            $convertedContent = $this->markdownParsedVariableProvider->convert($page);
            $components = $this->pageComponentsFactory->create($convertedContent['components']);

            if (!$this->dynamicTemplateDecider->hasAllNeededContent($page)) {
                return null;
            }

            return PageContentFactory::create($convertedContent['seo'], $components);
        } catch (InvalidArgumentException | ParseException | NodeException $e) {
            return null;
        }
    }
}
