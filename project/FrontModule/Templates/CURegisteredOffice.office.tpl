{include file="@header.tpl"}

{* JS ADDRESSES FOR PREFILL *}
<script type="text/javascript">
	var addresses = {$jsAdresses nofilter};
</script>

{literal}
<script type="text/javascript">
$(document).ready(function () {

	// start on beginning
	ourRegisterOfficeHandler();
	
	// called on type click
	$("input[name='ourRegisteredOffice']").click(function () {
		ourRegisterOfficeHandler(); 
	});
	
	
	/**
	 * Provides disable and enable dropdown for our offices
	 * @return void
	 */
	function ourRegisterOfficeHandler()
	{
		var disable = $("#ourRegisteredOffice").is(":checked");
		$('#prefill').attr("disabled", disable);
		$('#premise').attr("disabled", disable);
		$('#street').attr("disabled", disable);
		$('#thoroughfare').attr("disabled", disable);
		$('#post_town').attr("disabled", disable);
		$('#postcode').attr("disabled", disable);
		$('#county').attr("disabled", disable);
		$('#country').attr("disabled", disable);
	}
	
	// prefill address	
	$("#prefill").change(function () {
		var value = $("#prefill").val();
		address = addresses[value];
		for (var name in address) {
			$('#'+name).val(address[name]);
		}
	});
});
</script>
{/literal}
<div id="maincontent2" style="width: 100%">
	<p style="margin: 0 0 15px 0; font-size: 11px;"><a href="{$this->router->link("FrontModule\controlers\CUSummaryControler::SUMMARY_PAGE", "company_id=$companyId")}">{$companyName}</a> &gt; {$title}</p>
	{if $visibleTitle}
    <h1>{$title}</h1>
    {/if}
	{$form->getBegin() nofilter}
	<div class="row mtop20">

		{if isset($form.ourRegisteredOffice)}
			<div class="col-xs-12 own-address-offer" id="fieldset_1" style="padding: 10px">
				<div class="col-xs-12" id="fieldset_0">
					<div class="row">
						<div class="col-xs-12">
							{$form->getLabel('ourRegisteredOffice') nofilter}
							{$form->getControl('ourRegisteredOffice') nofilter}
						</div>
					</div>
				</div>
			</div>
		{/if}

		<div class="col-xs-12 own-address-offer" id="fieldset_1">
			<h3>Prefill Address</h3>
			<div class="row">
				<div class="col-xs-4">
					{$form->getLabel('prefill') nofilter}
				</div>
				<div class="col-md-7 col-xs-8">
					{$form->getControl('prefill') nofilter}
				</div>
				<div class="col-xs-1 hidden-sm hidden-xs">
					<div class="cf-help help-button">
						<a href="#" class="help-icon">help</a>
						<em>You can use this prefill option to select an address you&#39;ve entered previously.</em>
					</div>
				</div>
				<div class="col-xs-8 col-xs-offset-4 visible-sm visible-xs">
					<div class="help-block">
						You can use this prefill option to select an address you&#39;ve entered previously.
					</div>
				</div>
				<div class="col-xs-12 has-error">
					<div class="help-block">
						{$form->getError('prefill')}
					</div>
				</div>
			</div>
		</div>

		<div class="col-xs-12 own-address-offer" id="fieldset_2">
			<h3>Address</h3>
			<div class="row">
				<div class="col-xs-4">
					{$form->getLabel('premise') nofilter}
				</div>
				<div class="col-md-7 col-xs-8">
					{$form->getControl('premise') nofilter}
				</div>
				<div class="col-xs-1 hidden-sm hidden-xs">
					<div class="cf-help help-button">
						<a href="#" class="help-icon">help</a>
						<em>Eg. "28A" or "Flat 2, 36" or "Crusader House"</em>
					</div>
				</div>
				<div class="col-xs-8 col-xs-offset-4 visible-sm visible-xs">
					<div class="help-block">
						Eg. "28A" or "Flat 2, 36" or "Crusader House"
					</div>
				</div>
				<div class="col-xs-12 has-error">
					<div class="help-block">
						{$form->getError('premise')}
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-xs-4">
					{$form->getLabel('street') nofilter}
				</div>
				<div class="col-md-7 col-xs-8">
					{$form->getControl('street') nofilter}
				</div>
				<div class="col-xs-1 hidden-sm hidden-xs">
					<div class="cf-help help-button">
						<a href="#" class="help-icon">help</a>
						<em>Street refers to the actual street, road, lane etc.</em>
					</div>
				</div>
				<div class="col-xs-8 col-xs-offset-4 visible-sm visible-xs">
					<div class="help-block">
						Street refers to the actual street, road, lane etc.
					</div>
				</div>
				<div class="col-xs-12 has-error">
					<div class="help-block">
						{$form->getError('street')}
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-xs-4">
					{$form->getLabel('thoroughfare') nofilter}
				</div>
				<div class="col-xs-8">
					{$form->getControl('thoroughfare') nofilter}
				</div>
				<div class="col-xs-12 has-error">
					<div class="help-block">
						{$form->getError('thoroughfare')}
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-xs-4">
					{$form->getLabel('post_town') nofilter}
				</div>
				<div class="col-xs-8">
					{$form->getControl('post_town') nofilter}
				</div>
				<div class="col-xs-12 has-error">
					<div class="help-block">
						{$form->getError('post_town')}
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-xs-4">
					{$form->getLabel('county') nofilter}
				</div>
				<div class="col-xs-8">
					{$form->getControl('county') nofilter}
				</div>
				<div class="col-xs-12 has-error">
					<div class="help-block">
						{$form->getError('county')}
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-xs-4">
					{$form->getLabel('postcode') nofilter}
				</div>
				<div class="col-xs-8">
					{$form->getControl('postcode') nofilter}
				</div>
				<div class="col-xs-12 has-error">
					<div class="help-block">
						{$form->getError('postcode') nofilter}
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-xs-4">
					{$form->getLabel('country') nofilter}
				</div>
				<div class="col-xs-8">
					{$form->getControl('country') nofilter}
				</div>
				<div class="col-xs-12 has-error">
					<div class="help-block">
						{$form->getError('country')}
					</div>
				</div>
			</div>
		</div>
		{if $form->canShowNewConfirmationStatementFields()}
			<div class="col-xs-12 own-address-offer" id="fieldset_3">
				<h3>Personal details</h3>
				<div class="row">
					<div class="col-md-4 col-xs-4">
						{$form->getLabel('isAppropriateOfficeAddressStatement') nofilter}
					</div>
					<div class="col-md-1 col-xs-2" style="width: 10px;">
						{$form->getControl('isAppropriateOfficeAddressStatement') nofilter}
					</div>
					<div class="col-md-7 col-xs-6">
						<span id="appropriate-span">
							I confirm that the new registered office address is an appropriate address as outlined
							in section 86(2) of the Companies Act 2006
						</span>
					</div>
				</div>
				<div class="col-xs-12 has-error">
					<div class="help-block">
						{$form->getError('isAppropriateOfficeAddressStatement')}
					</div>
				</div>
			</div>
		{/if}
		<div class="col-xs-12 own-address-offer" id="fieldset_3" style="padding: 10px">
			<div class="col-xs-4">
				<h3 style="margin:0">Action</h3>
			</div>
			<div class="col-xs-8">
				{$form->getControl('save') nofilter}
			</div>
		</div>
	</div>
	{$form->getEnd() nofilter}
</div>
{include file="@footer.tpl"}
