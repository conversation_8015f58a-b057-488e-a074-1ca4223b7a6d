<?php

namespace LandingPagesModule\BrooksonOneModule\Emailers;

use EmailModule\IEmailGateway;
use LandingPagesModule\BrooksonOneModule\Dto\BrooksonOneApplicationDetails;
use LandingPagesModule\BrooksonOneModule\Factories\BrooksonOneEmailFactory;

class BrooksonOneEmailer
{
    /**
     * @var IEmailGateway
     */
    private $emailGateway;

    /**
     * @var BrooksonOneEmailFactory
     */
    private $emailFactory;

    public function __construct(IEmailGateway $emailGateway, BrooksonOneEmailFactory $emailFactory)
    {
        $this->emailGateway = $emailGateway;
        $this->emailFactory = $emailFactory;
    }

    public function send(BrooksonOneApplicationDetails $details): void
    {
        $this->emailGateway->send($this->emailFactory->createFromDetails($details));
    }
}
