<?php

namespace MarketingModule\Repositories;

use CompanyModule\Queries\CompanyQueryBuilder;
use Entities\Company;
use OrmModule\Repositories\IQueryableRepository;
use OrmModule\Repositories\IRepository;
use Utils\Date;

class CompanyRepository
{
    /**
     * @var array
     */
    protected $eligiblePackages = [1313, 1315, 1316, 1317, 1694];

    /**
     * @var IRepository
     */
    private $repository;

    /**
     * @param IQueryableRepository|IRepository $repository
     */
    public function __construct(IQueryableRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * @return Company[]
     */
    public function getCompaniesToExportForNamesco(Date $startDate, array $enabledPackageIds): array
    {
        $qb = new CompanyQueryBuilder($this->repository->createSimpleBuilder());
        return $qb->getCompanies()
            ->withProductIdIn($enabledPackageIds)
            ->withIncorporationAfter($startDate)
            ->withoutVoucher()
            ->getQueryResult();
    }
}
