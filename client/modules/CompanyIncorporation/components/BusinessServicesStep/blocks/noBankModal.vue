<template>
    <div class="modal fade" id="noBankModal" tabindex="-1" aria-labelledby="noBankModalLabel" aria-hidden="true" style="z-index: 100000;">
        <div class="modal-dialog modal-dialog-centered modal-xl" style="margin-top: -40px">
            <div class="modal-content">
                <div class="modal-header" style="padding: 40px 20px 40px 40px;">
                    <p class="modal-title fs-5 fw-bold">
                        Are you sure you don’t need a business bank account?
                    </p>
                    <i type="button" class="close fa-solid fa-xmark fa-lg text-danger mx-3" data-bs-dismiss="modal" aria-label="Close"></i>
                </div>
                <div class="modal-body p-4" style="padding-left: 40px!important;">
                    <ul>
                        <li class="fw-bold">It is mandatory to have a dedicated bank account for your business, as your business is legally a separated entity.</li>
                        <li>You will save time by keeping your company expenses isolated, simplifying your business accounting.</li>
                        <li>We have only selected the best banks, with simple online sign up processes (although, if you prefer, Barclays offer in-branch sign ups too).</li>
                    </ul>
                </div>
                <div class="border-top flex-column d-flex flex-lg-row flex-md-column flex-xl-row flex-sm-column justify-content-end p-4 gap-4">
                    <button type="submit" id="business_banking_no_bank_accont_button_96795459" class="cms-btn-outline" @click="$emit('skipBusinessBanking')" style="padding-top: 14px; padding-bottom: 14px;">
                        My Company does not need a bank account
                        <i class="fa fa-long-arrow-right ms-2"></i>
                    </button>

                    <button type="button" id="business_banking_select_my_company_button_80863143" data-bs-dismiss="modal" class="cms-btn py-3">
                        Let me select my company’s bank
                        <i class="fa fa-long-arrow-right ms-2"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    export default ({
        emits: ['skipBusinessBanking']
    })
</script>