export class CountryDecider {
  static isUkCountry (country) {
    const ukCountries = [
      'GBR', 'GB-ENG', 'GB-WLS', 'GB-SCT', 'GB-NIR',
      'United Kingdom', 'England', 'Wales', 'Scotland', 'Northern Ireland'
    ];
    return ukCountries.indexOf(country) !== -1;
  }

  static isNonForeignCountry (country) {
    const countries = [
      'USA', 'IRL', 'DEU', 'FRA', 'ITA', 'ESP', 'PRT', 'NLD', 'POL', 'BEL', 'NOR',
      'SWE', 'DNK', 'AUS', 'NZL', 'CAN', 'ZAF', 'AUT', 'HRV', 'CYP', 'CZE', 'EST',
      'HUN', 'GRC', 'LTU', 'GBR', 'GB-ENG', 'GB-WLS', 'GB-SCT', 'GB-NIR'
    ];
    return countries.indexOf(country) !== -1;
  }

  /*!
 * WHEN UPDATING THE LIST OF HIGH RISK COUNTRIES, REMEMBER TO ALSO UPDATE
 * project/CompaniesHouseModule/Config/countries.yml
 * www/components/jquery-validation/dist/additional-methods.js
 * and then run 'npm run compile'
 */
  static isHighRiskCountry (country) {
    // const highRiskCountries = [
    //   'Albania', 'AL', 'ALB',
    //   'Barbados', 'BB', 'BRB',
    //   'Burkina Faso', 'BF', 'BFA',
    //   'Cambodia', 'KH', 'KHM',
    //   'Cayman Islands', 'KY', 'CYM',
    //   "Korea's Democratic Peoples Republic Of", 'North Korea', 'KP', 'PRK',
    //   'Gibraltar', 'GI', 'GIB',
    //   'Haiti', 'HT', 'HTI',
    //   'Iran', 'IR', 'IRN',
    //   'Jamaica', 'JM', 'JAM',
    //   'Jordan', 'JO', 'JOR',
    //   'Mali', 'ML', 'MLI',
    //   'Morocco', 'MA', 'MAR',
    //   'Myanmar', 'MM', 'MMR',
    //   'Nicaragua', 'NI', 'NIC',
    //   'Pakistan', 'PK', 'PAK',
    //   'Panama', 'PA', 'PAN',
    //   'Philippines', 'PH', 'PHL',
    //   'Senegal', 'SN', 'SEN',
    //   'South Sudan', 'SS', 'SSD',
    //   'Syria', 'SY', 'SYR',
    //   'Turkey', 'Türkiye', 'Turkiye', 'TR', 'TUR',
    //   'Uganda', 'UG', 'UGA',
    //   'United Arab Emirates', 'AE', 'ARE',Uae,
    //   'Yemen', 'YE', 'YEM',
    //    Afghanistan, AF, AFG,
    //    Algeria, DZ, DZA,
    //    Angola, AO, AGO,
    //    Bulgaria, BG, BGR,
    //    Brazil, BR, BRA,
    //    Cameroon, CM, CMR,
    //    "Cote d'Ivoire", "Ivory Coast", "Côte d'Ivoire", CI, CIV,
    //    Croatia, HR, HRV,
    //    Cuba, CU, CUB,
    //    "Democratic Republic of the Congo", CD, COD,
    //    Egypt, EG, EGY,
    //    Iraq, IQ, IRQ,
    //    Israel, IL, ISR,
    //    Kenya, KE, KEN,
    //    Lebanon, LB, LBN,
    //    Namibia, NA, NAM,
    //    Monaco, MC, MCO,
    //    Mozambique, MZ, MOZ,
    //    "South Africa", ZA, ZAF,
    //    Sudan, SD, SDN,
    //    Tanzania, TZ, TZA,
    //    Venezuela, VE, VEN,
    //    Vietnam, VN, VNM,
    // ];

    return false; // TODO: remove when validations are removed

    // return highRiskCountries.indexOf(country) !== -1;
  }
}
