// @flow
import { IdForm } from './IdForm';
import { PCAPredictController } from '../PCAPredict/PCAPredictController';
import { idFormRules } from './Definitions/idFormDefinition';
import $ from 'jquery';
import rivets from 'rivets';
import { uiserver } from '../external';
import { setPostcodeValidationRules } from '../PCAPredict/GetAddress';

export class IdOptions {
    line1Url: string;
    line2Url: string;
    licenseUrl: string;
    useDateInTime: boolean;
    currentDocumentType: string;
    currentPassportCountry: string;
    currentPassportSecondNumber: string;
    address: Object;
}

export class IdValidationController {
  static bind (formName: string, key: string, options: IdOptions, postCodeFieldId: string) {
    const form = '[name="' + formName + '"]';
    const boundObject = IdValidationController.handle(form, key, options);
    window.personController = boundObject;

    const fields = IdForm.getDefaultFields(formName);
    const rules = idFormRules(form, fields, boundObject.pca, options.line1Url, options.line2Url, options.licenseUrl, boundObject.idForm, options.useDateInTime);
    $(form).validate(rules);

    // setting validation for new postcode lookup
    setPostcodeValidationRules(postCodeFieldId);

    return rivets.bind($(form), boundObject);
  }

  static handle (form: string, key: string, options: IdOptions): Object {
    const validationForm = new IdForm(
      options.currentDocumentType,
      options.currentPassportCountry,
      options.currentPassportSecondNumber
    );
    const pcaPredict = PCAPredictController.bindLines(key, options.address, form);
    const boundObject = {
      pca: pcaPredict,
      idForm: validationForm,
      useObjectContext: true
    };
    $(form).find('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
      validationForm.updateDocumentType(e, e.target);
    });
    uiserver.autofocus();
    return boundObject;
  }
}
