<?php

namespace tests\project\BusinessServicesModule\AnnaModule;

use BusinessServicesModule\AnnaModule\Commands\SubmitLeadsCommand;
use BusinessServicesModule\AnnaModule\Factories\LeadDataCreateFactory;
use BusinessServicesModule\AnnaModule\Factories\LeadsToProcessCriteriaFactory;
use BusinessServicesModule\ApiClient\BusinessServicesApiClient;
use BusinessServicesModule\Entities\Lead;
use BusinessServicesModule\Entities\Offer;
use BusinessServicesModule\Entities\PartnerServicesInformation;
use BusinessServicesModule\Repositories\ArrayOfferRepository;
use BusinessServicesModule\Repositories\LeadRepository;
use BusinessServicesModule\Responses\SuccessResponse;
use Entities\Company;
use Entities\Customer;
use Psr\Log\LoggerInterface;
use TestModule\Annotations\Inject;
use TestModule\Helpers\DatabaseHelper;
use TestModule\PhpUnit\TestCase;
use tests\helpers\CompanyHouse\IncorporationHelper;
use tests\helpers\EntityHelper;
use Utils\Date;

class SubmitLeadsCommandTest extends TestCase
{
    /**
     * @var LeadRepository
     */
    private $leadRepository;

    /**
     * @var LeadsToProcessCriteriaFactory
     */
    private $criteriaFactory;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var BusinessServicesApiClient
     */
    private $apiClient;

    /**
     * @var DatabaseHelper
     */
    private $databaseHelper;

    /**
     * @var SubmitLeadsCommand
     */
    private $command;

    /**
     * @var Customer
     */
    private $customer;

    /**
     * @var LeadDataCreateFactory
     */
    private $leadDataCriteriaFactory;

    /**
     * @Inject({
     *     "leadRepository" = "business_services_module.repositories.lead_repository",
     *     "logger" = "cron.loggers.default_logger",
     *     "criteriaFactory" = "business_services_module.anna_module.factories.leads_to_process_criteria_factory",
     *     "leadDataCriteriaFactory" = "business_services_module.anna_module.factories.lead_data_create_factory",
     *     "databaseHelper"="test_module.helpers.database_helper",
     *     "mediatorDecider"="business_services_module.deciders.mediator_decider"
     * })
     */
    public function setupDependencies(
        LeadRepository $leadRepository,
        LoggerInterface $logger,
        LeadsToProcessCriteriaFactory $criteriaFactory,
        LeadDataCreateFactory $leadDataCriteriaFactory,
        DatabaseHelper $databaseHelper
    )
    {
        $this->leadRepository = $leadRepository;
        $this->criteriaFactory = $criteriaFactory;
        $this->logger = $logger;
        $this->leadDataCriteriaFactory = $leadDataCriteriaFactory;
        $this->databaseHelper = $databaseHelper;
        $this->apiClient = $this->createMock(BusinessServicesApiClient::class);
        $this->command = new SubmitLeadsCommand(
            $this->leadRepository,
            $this->criteriaFactory,
            $this->logger,
            $this->leadDataCriteriaFactory,
            $this->apiClient
        );
    }

    public function testProductLeads(): void
    {
        $this->clearTables();
        $this->init();

        $lead1 = $this->databaseHelper->find(Lead::class, 1);
        $lead2 = $this->databaseHelper->find(Lead::class, 2);
        $lead3 = $this->databaseHelper->find(Lead::class, 3);

        $this->apiClient->expects($this->once())
            ->method('sendRequest')
            ->with($lead3, ArrayOfferRepository::ANNA_MEDIATOR_ID, $this->anything())
            ->willReturn(new SuccessResponse());

        $this->command->execute(false);

        $this->assertFalse($lead1->isProcessed());
        $this->assertFalse($lead2->isProcessed());
        $this->assertTrue($lead3->isProcessed());

        $this->clearTables();
    }

    public function tearDown(): void
    {
        $this->clearTables();
        EntityHelper::emptyTables([TBL_CUSTOMER_BUSINESS_INFO]);
    }

    private function clearTables(): void
    {
        EntityHelper::emptyTables(EntityHelper::$tables);
    }

    private function init(): void
    {
        for ($i = 1; $i <= 3; $i++) {
            $this->customer = EntityHelper::createCustomer(sprintf('<EMAIL>', $i));
            $this->customer->setDateOfBirth(new Date());
            $entities = IncorporationHelper::createReadyToBeIncorporated($this->customer, Company::COMPANY_CATEGORY_BYSHR);

            /** @var Company $company */
            $company = $entities['company'];

            if ($i === 3) {
                $company->setCompanyNumber(sprintf('1000000%s', $i));
                $company->setIncorporationDate(new Date('-1 month'));
            }

            $this->databaseHelper->saveEntities($entities);
            $this->databaseHelper->clearEntities();

            $company = $this->databaseHelper->find(Company::class, $company->getId());

            $lead = new Lead($company, Offer::ANNA_OFFER_ID, !($i === 1));

            $info = new PartnerServicesInformation();
            $info->setLead($lead);
            $info->setFirstName(sprintf('Tester - Lead Test %s', $i));
            $lead->setPartnerServicesInformation($info);

            $this->databaseHelper->saveEntity($lead);
        }
    }
}
