<?php

namespace Entities;

use DateTime;
use Utils\Date;
use Config\Constants\DiLocator;
use tests\helpers\EntityHelper;
use PHPUnit\Framework\TestCase;

/**
 * Generated by PHPUnit_SkeletonGenerator 1.2.1 on 2014-04-09 at 14:29:49.
 */
class ServiceDateTest extends TestCase
{
    /**
     * Sets up the fixture, for example, opens a network connection.
     * This method is called before a test is executed.
     */
    protected function setUp(): void
    {
        EntityHelper::emptyTables(array(TBL_CUSTOMERS, TBL_COMPANY, TBL_ORDERS, TBL_SERVICES));
    }

    /**
     * @covers Entities\Service::getDtExpires
     */
    public function testGetDtExpiresWithInitialDateObject()
    {
        $serviceId = $this->createService(new Date());
        $serviceService = EntityHelper::getService(DiLocator::SERVICE_SERVICE);
        $service = $serviceService->getServiceById($serviceId);
        $this->assertInstanceOf('Utils\Date', $service->getDtExpires());
        $this->assertInstanceOf('DateTime', $service->getDtExpires());
    }

    /**
     * @covers Entities\Service::getDtExpires
     */
    public function testGetDtExpiresWithInitialDateTimeObject()
    {
        $serviceId = $this->createService(new DateTime());
        $serviceService = EntityHelper::getService(DiLocator::SERVICE_SERVICE);
        $service = $serviceService->getServiceById($serviceId);
        $this->assertInstanceOf('Utils\Date', $service->getDtExpires());
        $this->assertInstanceOf('DateTime', $service->getDtExpires());
    }

    /**
     * @covers Entities\Service::getDtExpires
     */
    public function testGetDtExpiresWithInMemoryDateObject()
    {
        $serviceId = $this->createService(new Date(), FALSE);
        $serviceService = EntityHelper::getService(DiLocator::SERVICE_SERVICE);
        $service = $serviceService->getServiceById($serviceId);
        $this->assertInstanceOf('Utils\Date', $service->getDtExpires());
        $this->assertInstanceOf('DateTime', $service->getDtExpires());
    }

    /**
     * @covers Entities\Service::getDtExpires
     */
    public function testGetDtExpiresWithInMemoryDateTimeObject()
    {
        $serviceId = $this->createService(new DateTime(), FALSE);
        $serviceService = EntityHelper::getService(DiLocator::SERVICE_SERVICE);
        $service = $serviceService->getServiceById($serviceId);
        $this->assertNotInstanceOf('Utils\Date', $service->getDtExpires());
        $this->assertInstanceOf('DateTime', $service->getDtExpires());
    }

    /**
     * @param mixed $dtExpires
     * @return int
     */
    private function createService($dtExpires, $clear = TRUE)
    {
        $customer = EntityHelper::createCustomer();
        $company = EntityHelper::createCompany($customer);
        $order = EntityHelper::createOrder($customer);
        $service = EntityHelper::createService($company, $order->getItems()->first(), NULL, $dtExpires);
        if ($clear) {
            EntityHelper::clear();
        }
        return $service->getId();
    }
}
