<?php

namespace UserModule\Commands;

use TestModule\PhpUnit\TestCase;
use TestModule\Annotations\Inject;
use TestModule\Helpers\DatabaseHelper;
use tests\helpers\EntityHelper;
use UserModule\Entities\AuthToken;
use Utils\Date;

class RemoveExpiredAuthTokensCommandTest extends TestCase
{
    /**
     * @var RemoveExpiredAuthTokensCommand
     */
    private $object;

    /**
     * @var DatabaseHelper
     */
    private $databaseHelper;

    /**
     * @Inject({
     *     "object"="user_module.commands.remove_expired_auth_tokens_command",
     *     "databaseHelper"="test_module.database_helper"
     *  })
     */
    public function setupDependencies(DatabaseHelper $databaseHelper, RemoveExpiredAuthTokensCommand $object)
    {
        $this->databaseHelper = $databaseHelper;
        $this->object = $object;
        $this->databaseHelper->emptyTables(EntityHelper::$tables);
    }

    /**
     * @covers Cron\Commands\Auth\AuthTokens::execute
     * @throws \Exception
     */
    public function testExecute()
    {
        $tokens = [];
        $customer = EntityHelper::createCustomer();

        $validUntil = new Date('-1 days');
        $tokens[] = AuthToken::createOneTimePassword($customer, "token-string-otp-X", $validUntil);
        $tokens[] = AuthToken::createForgottenPassword($customer, "token-string-fp-X", $validUntil);

        for ($i = 0; $i < 2; $i++)
        {
            $validUntil = new Date('+1 days');
            $token = sprintf("token-string-otp-%s", $i);
            $tokens[] = AuthToken::createOneTimePassword($customer, $token, $validUntil);
            $token = sprintf("token-string-fp-%s", $i);
            $tokens[] = AuthToken::createForgottenPassword($customer, $token, $validUntil);
        }

        $this->databaseHelper->saveEntities($tokens);
        $expiredTokens = [$tokens[0]->getId(), $tokens[1]->getId()];

        $this->object->execute();

        $em = EntityHelper::getEntityManager();

        $this->assertNull($em->find(AuthToken::class, $expiredTokens[0]));
        $this->assertNull($em->find(AuthToken::class, $expiredTokens[1]));

        $this->assertNotNull($em->find(AuthToken::class, $tokens[2]->getId()));
        $this->assertNotNull($em->find(AuthToken::class, $tokens[3]->getId()));
        $this->assertNotNull($em->find(AuthToken::class, $tokens[4]->getId()));
        $this->assertNotNull($em->find(AuthToken::class, $tokens[5]->getId()));

    }


}
