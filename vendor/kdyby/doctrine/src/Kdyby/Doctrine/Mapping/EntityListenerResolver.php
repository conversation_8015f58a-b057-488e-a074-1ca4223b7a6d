<?php

namespace K<PERSON>by\Doctrine\Mapping;

use <PERSON><PERSON><PERSON>;
use Nette;


class EntityListenerResolver implements \Doctrine\ORM\Mapping\EntityListenerResolver
{

	public function __construct(private readonly Nette\DI\Container $serviceLocator)
 {
 }



	/**
	 * {@inheritdoc}
	 */
	public function clear($className = null)
	{

	}



	/**
     * Returns a entity listener instance for the given class name.
     *
     * @param string $className The fully-qualified class name
     *
     * @return object|null An entity listener
     */
	public function resolve($className)
	{
		return $this->serviceLocator->getByType($className);
	}



	/**
	 * {@inheritdoc}
	 */
	public function register($object)
	{

	}

}
