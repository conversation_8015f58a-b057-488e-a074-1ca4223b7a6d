<?php

namespace MsgDomain;

use function FunctionalModule\Monads\Result\tryCatch;
use function MsgDomain\Strings\limitedFromString;
use const MsgDomain\Strings\limitedFromString;
use MsgDomain\Strings\LimitedString;
use MsgDomain\Strings\NotEmptyString;
use TestModule\PhpUnit\TestCase;

class LimitedStringTest extends TestCase
{
    public function testCreate()
    {
        $this->assertEquals(limitedFromString('a b c', 5), 'a b c');
        $this->assertEquals(limitedFromString('a', 1), 'a');
        $this->assertEquals(limitedFromString('ab', 3), 'ab');
        $this->assertEquals(new LimitedString(new NotEmptyString('abcdabcd'), 8, 8), 'abcdabcd');
    }

    public function testFailure()
    {
        $this->assertResultError(tryCatch(limitedFromString, ['abc', 2]));
        $this->assertResultError(tryCatch(limitedFromString, ['ab', 3, 3]));
        $this->assertResultError(tryCatch(limitedFromString, ['abc', 4, 10]));
        $this->assertResultError(tryCatch(limitedFromString, [' ', 2]));
        $this->assertResultError(tryCatch(limitedFromString, ['    ', 6]));
        $this->assertResultError(tryCatch(limitedFromString, [chr(11), 1]));
    }
}