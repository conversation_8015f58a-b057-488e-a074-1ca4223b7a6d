<?php

namespace MsgDomain;

use function FunctionalModule\Monads\Result\tryCatch;
use function MsgDomain\Strings\notEmptyFromString;
use const MsgDomain\Strings\notEmptyFromString;
use MsgDomain\Strings\NotEmptyString;
use TestModule\PhpUnit\TestCase;

class NotEmptyStringTest extends TestCase
{
    public function testCreate()
    {
        $this->assertEquals(new NotEmptyString('a'), 'a');
        $this->assertEquals(new NotEmptyString('LONG'), 'LONG');
        $this->assertEquals(notEmptyFromString(' LONG '), ' LONG ');
        $this->assertEquals(notEmptyFromString(' 小组 '), ' 小组 ');
        $this->assertEquals(new NotEmptyString('3'), '3');
    }

    public function testFailure()
    {
        // keep non printable ?
        $this->assertResultError(tryCatch(notEmptyFromString, [chr(11)]));

        $this->assertResultError(tryCatch(notEmptyFromString, ['']));
        $this->assertResultError(tryCatch(notEmptyFromString, [' ']));
        $this->assertResultError(tryCatch(notEmptyFromString, ['    ']));
        $this->assertResultError(tryCatch(notEmptyFromString, ["\n"]));
    }
}