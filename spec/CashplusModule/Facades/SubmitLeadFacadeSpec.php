<?php

namespace spec\CashplusModule\Facades;

use BusinessServicesModule\Entities\Lead;
use BusinessServicesModule\Entities\PartnerServicesInformation;
use BusinessServicesModule\Exceptions\LeadSubmissionFailedException;
use CashplusModule\Clients\Client;
use CashplusModule\Dto\LtdApplicationData;
use CashplusModule\Entities\PartnerCashplusService;
use CashplusModule\Facades\SubmitLeadFacade;
use CashplusModule\Factories\LtdApplicationDataFactory;
use CompanySyncModule\Services\ISynchronization;
use CompanySyncModule\Services\SynchronizationService;
use DateTime;
use Doctrine\ORM\EntityManager;
use Entities\Company;
use HttpClient\Responses\ResponseInterface;
use PhpSpec\ObjectBehavior;
use Prophecy\Argument;
use Psr\Log\LoggerInterface;
use SerializingModule\Context;
use SerializingModule\SerializerInterface;
use Symfony\Component\HttpFoundation\Response;

class SubmitLeadFacadeSpec extends ObjectBehavior
{
    private const TOKEN = '123456';
    private const DUMMY_RESPONSE = ['nope' => 'nope'];
    private const DUMMY_DATA = ['test' => 'test'];

    /**
     * @var Client
     */
    private $client;

    /**
     * @var LtdApplicationDataFactory
     */
    private $factory;

    /**
     * @var EntityManager
     */
    private $em;

    /**
     * @var SerializerInterface
     */
    private $serializer;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var SynchronizationService
     */
    private $synchronizationService;

    function let(
        Client $client,
        LtdApplicationDataFactory $factory,
        EntityManager $em,
        SerializerInterface $serializer,
        LoggerInterface $logger,
        SynchronizationService $synchronizationService
    ) {
        $this->client = $client;
        $this->factory = $factory;
        $this->em = $em;
        $this->serializer = $serializer;
        $this->logger = $logger;
        $this->synchronizationService = $synchronizationService;

        $this->beConstructedWith($client, $factory, $em, $serializer, $logger, $synchronizationService);
    }

    function it_is_initializable()
    {
        $this->shouldHaveType(SubmitLeadFacade::class);
    }

    function it_should_submit_lead(
        Lead $lead,
        PartnerCashplusService $service,
        Company $company,
        PartnerServicesInformation $partnerServicesInformation,
        LtdApplicationData $applicationData
    ) {
        $lead->getId()->willReturn(1);
        $lead->getPartnerCashplusService()->willReturn($service);
        $lead->getCompany()->willReturn($company);
        $lead->getPartnerServicesInformation()->willReturn($partnerServicesInformation);

        $this->synchronizationService->synchronizeCompany($company)->shouldBeCalled();

        $this->factory->createLtdApplicationData($company, $partnerServicesInformation, $service)->willReturn($applicationData);

        $service->setDateSent(Argument::type(DateTime::class))->shouldBeCalled();

        $data = json_encode(self::DUMMY_DATA);
        $this->serializer->serialize($applicationData, SerializerInterface::FORMAT_JSON, new Context(TRUE))->willReturn($data);
        $service->setRequestBody($data)->shouldBeCalled();

        $response = ['message' => 'hello', 'referenceNumber' => 123];
        $this->client->submitLead(self::TOKEN, $applicationData)->willReturn($response);

        $service->setResponseCode(ResponseInterface::OK)->shouldBeCalled();
        $service->setMessage('hello')->shouldBeCalled();
        $service->setResponseBody(json_encode($response))->shouldBeCalled();
        $service->setReferenceNumber(123)->shouldBeCalled();
        $lead->markAsProcessed()->shouldBeCalled();

        $this->em->flush([$lead, $service])->shouldBeCalled();

        $this->submitLead(self::TOKEN, $lead)->shouldBe(true);
    }

    function it_should_submit_lead_and_fail(
        Lead $lead,
        PartnerCashplusService $service,
        Company $company,
        PartnerServicesInformation $partnerServicesInformation,
        LtdApplicationData $applicationData
    ) {
        $lead->getId()->willReturn(1);
        $lead->getPartnerCashplusService()->willReturn($service);
        $lead->getCompany()->willReturn($company);
        $lead->getPartnerServicesInformation()->willReturn($partnerServicesInformation);

        $this->factory->createLtdApplicationData($company, $partnerServicesInformation, $service)->willReturn($applicationData);

        $service->setDateSent(Argument::type(DateTime::class))->shouldBeCalled();

        $data = json_encode(self::DUMMY_DATA);
        $this->serializer->serialize($applicationData, SerializerInterface::FORMAT_JSON, new Context(TRUE))->willReturn($data);
        $service->setRequestBody($data)->shouldBeCalled();

        $response = json_encode(self::DUMMY_RESPONSE);
        $exception = new LeadSubmissionFailedException($response, Response::HTTP_UNAUTHORIZED);
        $this->client->submitLead(self::TOKEN, $applicationData)->willThrow($exception);

        $lead->markAsProcessed()->shouldNotBeCalled();

        $service->setResponseCode(Response::HTTP_UNAUTHORIZED)->shouldBeCalled();
        $service->setResponseBody($response)->shouldBeCalled();

        $this->em->flush([$lead, $service])->shouldBeCalled();

        $this->submitLead(self::TOKEN, $lead)->shouldBe(false);
    }

    function it_should_submit_lead_and_success_since_its_duplicate(
        Lead $lead,
        PartnerCashplusService $service,
        Company $company,
        PartnerServicesInformation $partnerServicesInformation,
        LtdApplicationData $applicationData
    ) {
        $lead->getId()->willReturn(1);
        $lead->getPartnerCashplusService()->willReturn($service);
        $lead->getCompany()->willReturn($company);
        $lead->getPartnerServicesInformation()->willReturn($partnerServicesInformation);

        $this->factory->createLtdApplicationData($company, $partnerServicesInformation, $service)->willReturn($applicationData);

        $service->setDateSent(Argument::type(DateTime::class))->shouldBeCalled();

        $data = json_encode(self::DUMMY_DATA);
        $this->serializer->serialize($applicationData, SerializerInterface::FORMAT_JSON, new Context(TRUE))->willReturn($data);
        $service->setRequestBody($data)->shouldBeCalled();

        $response = json_encode(self::DUMMY_RESPONSE);
        $exception = new LeadSubmissionFailedException($response, Response::HTTP_CONFLICT);
        $this->client->submitLead(self::TOKEN, $applicationData)->willThrow($exception);

        $lead->markAsProcessed()->shouldBeCalled();

        $service->setResponseCode(Response::HTTP_CONFLICT)->shouldBeCalled();
        $service->setResponseBody($response)->shouldBeCalled();

        $this->em->flush([$lead, $service])->shouldBeCalled();

        $this->submitLead(self::TOKEN, $lead)->shouldBe(false);
    }
}
