<?php

declare(strict_types=1);

namespace spec\CompanyIncorporationModule\UseCase\BusinessServices\Api\ProcessBusinessOffers;

use BusinessServicesModule\Facades\SelectOffersFacade;
use CompanyIncorporationModule\UseCase\BusinessServices\Api\ProcessBusinessOffers\Command;
use CompanyIncorporationModule\UseCase\BusinessServices\Api\ProcessBusinessOffers\Factory;
use CompanyIncorporationModule\UseCase\BusinessServices\Api\ProcessBusinessOffers\Request;
use CompanyIncorporationModule\UseCase\BusinessServices\Api\ProcessBusinessOffers\Response;
use Entities\Company;
use Entities\Customer;
use PhpSpec\ObjectBehavior;
use PhpSpec\Wrapper\Collaborator;
use Services\EventService;

class CommandSpec extends ObjectBehavior
{
    private Collaborator|Factory $factory;
    private Collaborator|SelectOffersFacade $selectOffersFacade;
    private Collaborator|EventService $eventService;

    public function let(
        Factory $factory,
        SelectOffersFacade $selectOffersFacade,
        EventService $eventService,
    ): void {
        $this->factory = $factory;
        $this->selectOffersFacade = $selectOffersFacade;
        $this->eventService = $eventService;

        $this->beConstructedWith($factory, $selectOffersFacade, $eventService);
    }

    public function it_is_initializable(): void
    {
        $this->shouldHaveType(Command::class);
    }

    public function it_executes_and_processes_business_offers(
        Response $response,
    ): void {
        $offers = [
            ['categoryId' => 1, 'selectedOfferId' => 10],
            ['categoryId' => 1, 'selectedOfferId' => 11],
            ['categoryId' => 2, 'selectedOfferId' => 20],
        ];
        $company = new Company(new Customer('test', 'test'), 'test');
        $request = new Request($company, $offers);

        $this->eventService
            ->notifyPreventDuplicationCached(Command::EVENT_NAME, $company->getId())
            ->shouldBeCalled();

        $this->eventService
            ->notifyPreventDuplicationCached(Command::EVENT_TOOLKIT_NAME_SKIPPED, $company->getId())
            ->shouldBeCalled();

        $this->selectOffersFacade
            ->setAllNonBankingUnprocessedLeadsAsDeleted($company)
            ->shouldBeCalled();

        $this->selectOffersFacade
            ->processOffers($company, [10, 11], 1)
            ->shouldBeCalled();

        $this->selectOffersFacade
            ->processOffers($company, [20], 2)
            ->shouldBeCalled();

        $this->selectOffersFacade
            ->setDuplicateLeadsAsDeleted($company)
            ->shouldBeCalled();

        $this->factory->makeResponse()->willReturn($response);

        $this->execute($request)->shouldReturn($response);
    }

    public function it_skips_when_no_offers_or_toolkit_offers(
        Response $response,
    ): void {
        $company = new Company(new Customer('test', 'test'), 'test');
        $request = new Request($company, []);

        $this->eventService
            ->notifyPreventDuplicationCached(Command::EVENT_NAME_SKIPPED, $company->getId())
            ->shouldBeCalled();

        $this->eventService
            ->notifyPreventDuplicationCached(Command::EVENT_TOOLKIT_NAME_SKIPPED, $company->getId())
            ->shouldBeCalled();

        $this->selectOffersFacade
            ->setAllNonBankingUnprocessedLeadsAsDeleted($company)
            ->shouldBeCalled();

        $this->selectOffersFacade
            ->setDuplicateLeadsAsDeleted($company)
            ->shouldBeCalled();

        $this->factory->makeResponse()->willReturn($response);

        $this->execute($request)->shouldReturn($response);
    }
}
